#!/usr/bin/env python3
"""
Test script to check the agents list endpoint
"""

import requests
import json

BASE_URL = "http://localhost:8000"

def test_agents_list():
    """Test the agents list endpoint"""
    url = f"{BASE_URL}/agents/list"
    print(f"🧪 Testing: Agents List")
    print(f"📍 URL: {url}")
    
    try:
        response = requests.get(url, timeout=10)
        print(f"✅ Status: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"📊 Found {len(data)} agents:")
            for agent in data[:5]:  # Show first 5 agents
                print(f"  - ID: {agent['id']}, Name: {agent['name']}")
            if len(data) > 5:
                print(f"  ... and {len(data) - 5} more")
            return data
        else:
            print(f"❌ Error: {response.text}")
            return None
            
    except requests.exceptions.ConnectionError:
        print("❌ Connection Error: Is the backend server running?")
        print("💡 Start it with: cd rpay-analytics-api && source .venv/bin/activate && uvicorn app.main:app --reload")
        return None
    except Exception as e:
        print(f"❌ Error: {str(e)}")
        return None

if __name__ == "__main__":
    agents = test_agents_list()
    
    if agents and len(agents) > 0:
        print(f"\n🎉 Success! Found {len(agents)} agents in the system.")
        print("✅ The agent selector will now show real agent data instead of mock data.")
    else:
        print("\n⚠️  No agents found. Make sure you have transaction data loaded in the backend.")
