#!/usr/bin/env python3
"""
Debug script to check backend status and identify 500 errors
"""

import requests
import json

BASE_URL = "http://localhost:8000"

def check_endpoint(endpoint: str, description: str):
    """Check a single endpoint and show detailed error info"""
    url = f"{BASE_URL}{endpoint}"
    print(f"\n🔍 Checking: {description}")
    print(f"📍 URL: {url}")
    
    try:
        response = requests.get(url, timeout=10)
        print(f"Status: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Success - Response type: {type(data)}")
            if isinstance(data, list):
                print(f"   Items: {len(data)}")
            elif isinstance(data, dict):
                print(f"   Keys: {list(data.keys())}")
        else:
            print(f"❌ Error {response.status_code}")
            try:
                error_data = response.json()
                print(f"   Error details: {json.dumps(error_data, indent=2)}")
            except:
                print(f"   Error text: {response.text}")
                
    except requests.exceptions.ConnectionError:
        print("❌ Connection Error: Backend not running")
    except Exception as e:
        print(f"❌ Exception: {str(e)}")

def main():
    print("🔧 RPAY Backend Debug Tool")
    print("=" * 40)
    
    # Check basic endpoints
    check_endpoint("/", "Root endpoint")
    check_endpoint("/docs", "API Documentation")
    check_endpoint("/agents/list", "Agents List")
    check_endpoint("/agents/count", "Agents Count")
    
    # Try to get first agent and test its endpoints
    try:
        response = requests.get(f"{BASE_URL}/agents/list", timeout=10)
        if response.status_code == 200:
            agents = response.json()
            if agents and len(agents) > 0:
                agent_id = agents[0]["id"]
                print(f"\n📋 Testing with agent: {agent_id}")
                
                check_endpoint(f"/agents/{agent_id}/stats", f"Agent {agent_id} Stats")
                check_endpoint(f"/agents/{agent_id}/overview", f"Agent {agent_id} Overview")
            else:
                print("\n⚠️  No agents found - this might be the issue!")
                print("   Make sure you have transaction data loaded in the backend.")
        else:
            print(f"\n❌ Cannot get agents list: {response.status_code}")
    except Exception as e:
        print(f"\n❌ Error getting agents: {str(e)}")
    
    print("\n" + "=" * 40)
    print("💡 Common Issues:")
    print("1. Backend not running - start with: uvicorn app.main:app --reload")
    print("2. No transaction data - upload a CSV file to the backend")
    print("3. Database/CSV file issues - check backend logs")
    print("4. CORS issues - check browser console for CORS errors")

if __name__ == "__main__":
    main()
