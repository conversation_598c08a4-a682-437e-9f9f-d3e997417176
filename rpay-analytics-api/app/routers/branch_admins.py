from io import String<PERSON>
from fastapi import APIRouter, Depends, HTTPException, Query, Body
from fastapi.responses import StreamingResponse
from ..core.data import get_df
from ..models.stats import SimpleStat, GraphData, TableData
import pandas as pd
from app.utils.router_helpers import filter_entity_data
from app.logic.branch_admins import (
        get_transaction_volume_over_time, get_customer_segmentation, get_transaction_outliers,
        get_top_customers, get_transaction_count_over_time, get_average_transaction_over_time,
        get_days_between_transactions, get_branch_admin_stats
    )
from app.logic.agents import get_transaction_frequency_analysis
from typing import List, Dict, Any
from app.utils.helpers import filter_transactions
from app.utils.analytics import _apply_date_filters

router = APIRouter(prefix="/branch-admins", tags=["Branch Admins"])

@router.get("/count", response_model=SimpleStat)
def total_branch_admins(df = Depends(get_df)):
    count = df["branch_admin_id"].nunique()
    return SimpleStat(metric="Unique Branch Admin Count", value=count)

@router.get("/{branch_admin_id}")
def get_branch_details(branch_admin_id: str, df=Depends(get_df)):
    """Get branch details."""
    branch_data = df[df['branch_admin_id'] == branch_admin_id]
    if branch_data.empty:
        raise HTTPException(status_code=404, detail=f"Branch {branch_admin_id} not found")

    # Get the first row to extract branch details
    first_row = branch_data.iloc[0]

    return {
        "branch_admin_id": branch_admin_id,
        "branch_name": first_row.get('branch_name', f'Branch {branch_admin_id}'),
        "merchant_id": first_row.get('merchant_id'),
        "merchant_name": first_row.get('merchant_name')
    }

@router.get("/{branch_admin_id}/stats", response_model=List[SimpleStat])
def branch_admin_stats(
    branch_admin_id: str,
    year: int = None,
    month: int = None,
    week: int = None,
    day: int = Query(None, ge=1, le=31),
    range_days: int = Query(None, ge=1),
    start_date: str = None,
    end_date: str = None,
    channel: str = None,
    df=Depends(get_df)
):
    # Use the helper function to filter data
    df, filters = filter_entity_data(
        df, "branch_admin_id", branch_admin_id,
        year, month, week, day, range_days, start_date, end_date, channel
    )

    return get_branch_admin_stats(df, filters)


@router.get("/{branch_admin_id}/terminals")
def get_branch_terminals(
    branch_admin_id: str,
    page: int = Query(1, ge=1),
    page_size: int = Query(20, ge=1, le=100),
    sort_by: str = Query("total_amount", pattern="^(terminal_id|total_amount|transaction_count)$"),
    sort_order: str = Query("desc", pattern="^(asc|desc)$"),
    search: str = Query("", description="Search in terminal ID"),
    df=Depends(get_df)
):
    """Get paginated terminals for a branch."""
    branch_df = df[df['branch_admin_id'] == branch_admin_id]

    if branch_df.empty:
        raise HTTPException(status_code=404, detail=f"No data found for branch {branch_admin_id}")

    # Group by terminal and calculate aggregates
    terminal_stats = branch_df.groupby('terminal_id').agg({
        'amount': ['sum', 'count']
    }).reset_index()

    terminal_stats.columns = ['terminal_id', 'total_amount', 'transaction_count']

    # Apply search filter
    if search:
        search_mask = terminal_stats['terminal_id'].str.contains(search, case=False, na=False)
        terminal_stats = terminal_stats[search_mask]

    # Apply sorting
    ascending = sort_order == "asc"
    terminal_stats = terminal_stats.sort_values(by=sort_by, ascending=ascending)

    # Calculate pagination
    total_items = len(terminal_stats)
    total_pages = (total_items + page_size - 1) // page_size
    start_idx = (page - 1) * page_size
    end_idx = start_idx + page_size

    # Get paginated data
    paginated_data = terminal_stats.iloc[start_idx:end_idx]

    return {
        "data": paginated_data.to_dict(orient="records"),
        "pagination": {
            "page": page,
            "page_size": page_size,
            "total_items": total_items,
            "total_pages": total_pages
        },
        "filters": {
            "search": search
        },
        "sort": {
            "sort_by": sort_by,
            "sort_order": sort_order
        }
    }

@router.get("/{branch_admin_id}/terminals/list", response_model=list[str])
def get_terminals_list_by_branch_admin(
    branch_admin_id: str,
    df=Depends(get_df)
):
    """Get simple list of terminal IDs for a branch (for backward compatibility)."""
    if "branch_admin_id" not in df.columns or "terminal_id" not in df.columns:
        raise HTTPException(status_code=500, detail="Required columns missing from dataset")

    df = df[df["branch_admin_id"] == branch_admin_id]

    if df.empty:
        raise HTTPException(status_code=404, detail="No terminals found for this branch admin")

    terminal_ids = df["terminal_id"].dropna().unique().tolist()
    return terminal_ids


@router.get("/{branch_admin_id}/overview")
def branch_admin_overview(
    branch_admin_id: str,
    granularity: str = Query("monthly", pattern="^(daily|weekly|monthly|yearly)$"),
    top_mode: str = Query("amount", pattern="^(amount|count)$"),
    top_limit: int = Query(10, ge=1),
    year: int = None,
    month: int = None,
    week: int = None,
    day: int = Query(None, ge=1, le=31),
    range_days: int = Query(None, ge=1),
    start_date: str = None,
    end_date: str = None,
    channel: str = None,
    df=Depends(get_df)
):
    # Use the helper function to filter data
    df, filters = filter_entity_data(
        df, "branch_admin_id", branch_admin_id,
        year, month, week, day, range_days, start_date, end_date, channel
    )

    return {
        "transaction_volume": get_transaction_volume_over_time(df, granularity),
        "transaction_count": get_transaction_count_over_time(df, granularity, filters),
        "average_transactions": get_average_transaction_over_time(df, granularity, filters),
        "segmentation": get_customer_segmentation(df, filters),
        "top_customers": get_top_customers(df, top_mode, top_limit, filters),
        "transaction_outliers": get_transaction_outliers(df, filters),
        "days_between_transactions": get_days_between_transactions(df, filters)
    }


@router.get("/{branch_admin_id}/average-transactions", response_model=GraphData)
def branch_admin_average_transactions(
    branch_admin_id: str,
    granularity: str = Query(..., pattern="^(daily|weekly|monthly|yearly)$"),
    year: int = None,
    month: int = None,
    week: int = None,
    day: int = Query(None, ge=1, le=31),
    range_days: int = Query(None, ge=1),
    start_date: str = None,
    end_date: str = None,
    channel: str = None,
    df=Depends(get_df)
):
    # Use the helper function to filter data
    df, filters = filter_entity_data(
        df, "branch_admin_id", branch_admin_id,
        year, month, week, day, range_days, start_date, end_date, channel
    )

    return get_average_transaction_over_time(df, granularity, filters)


@router.get("/{branch_admin_id}/segmentation", response_model=TableData)
def branch_admin_customer_segmentation(
    branch_admin_id: str,
    year: int = None,
    month: int = None,
    week: int = None,
    day: int = Query(None, ge=1, le=31),
    range_days: int = Query(None, ge=1),
    start_date: str = None,
    end_date: str = None,
    channel: str = None,
    df=Depends(get_df)
):
    # Use the helper function to filter data
    df, filters = filter_entity_data(
        df, "branch_admin_id", branch_admin_id,
        year, month, week, day, range_days, start_date, end_date, channel
    )

    return get_customer_segmentation(df, filters)


@router.get("/{branch_admin_id}/top-customers", response_model=TableData)
def top_customers_per_branch_admin(
    branch_admin_id: str,
    mode: str = Query(..., pattern="^(amount|count)$"),
    limit: int = Query(10, ge=1),
    year: int = None,
    month: int = None,
    week: int = None,
    day: int = Query(None, ge=1, le=31),
    range_days: int = Query(None, ge=1),
    start_date: str = None,
    end_date: str = None,
    channel: str = None,
    df=Depends(get_df)
):
    # Use the helper function to filter data
    df, filters = filter_entity_data(
        df, "branch_admin_id", branch_admin_id,
        year, month, week, day, range_days, start_date, end_date, channel
    )

    return get_top_customers(df, mode, limit, filters)

@router.get("/{branch_admin_id}/top-terminals")
def get_branch_top_terminals(
    branch_admin_id: str,
    mode: str = Query("amount", pattern="^(amount|count)$"),
    limit: int = Query(10, ge=1, le=100),
    year: int = None,
    month: int = None,
    week: int = None,
    day: int = Query(None, ge=1, le=31),
    range_days: int = Query(None, ge=1),
    start_date: str = None,
    end_date: str = None,
    channel: str = None,
    df=Depends(get_df)
):
    """Get top terminals for a branch."""
    # Use the helper function to filter data
    df, filters = filter_entity_data(
        df, "branch_admin_id", branch_admin_id,
        year, month, week, day, range_days, start_date, end_date, channel
    )

    # Calculate top terminals by amount or count
    if mode == "amount":
        grouped = df.groupby('terminal_id').agg({
            'amount': ['sum', 'count']
        }).reset_index()
        grouped.columns = ['terminal_id', 'total_amount', 'transaction_count']
        sorted_data = grouped.sort_values('total_amount', ascending=False).head(limit)
        metric = f"Top {limit} Terminals by Amount"
    else:  # mode == "count"
        grouped = df.groupby('terminal_id').agg({
            'amount': ['sum', 'count']
        }).reset_index()
        grouped.columns = ['terminal_id', 'total_amount', 'transaction_count']
        sorted_data = grouped.sort_values('transaction_count', ascending=False).head(limit)
        metric = f"Top {limit} Terminals by Transaction Count"

    return TableData(
        metric=metric,
        data=sorted_data.to_dict(orient="records")
    )

@router.get("/{branch_admin_id}/terminal-activity-heatmap")
def get_branch_terminal_activity_heatmap(
    branch_admin_id: str,
    granularity: str = Query("monthly", pattern="^(daily|weekly|monthly|yearly)$"),
    year: int = None,
    month: int = None,
    week: int = None,
    day: int = Query(None, ge=1, le=31),
    range_days: int = Query(None, ge=1),
    start_date: str = None,
    end_date: str = None,
    channel: str = None,
    df=Depends(get_df)
):
    """Get terminal activity heatmap for a branch."""
    # Use the helper function to filter data
    df, filters = filter_entity_data(
        df, "branch_admin_id", branch_admin_id,
        year, month, week, day, range_days, start_date, end_date, channel
    )

    # Create terminal activity heatmap
    try:
        # Group by terminal and time period
        df['date'] = pd.to_datetime(df['date'])

        if granularity == 'daily':
            df['period'] = df['date'].dt.strftime('%Y-%m-%d')
        elif granularity == 'weekly':
            df['period'] = df['date'].dt.strftime('%Y-W%U')
        elif granularity == 'monthly':
            df['period'] = df['date'].dt.strftime('%Y-%m')
        else:  # yearly
            df['period'] = df['date'].dt.strftime('%Y')

        # Get unique periods and terminals
        periods = sorted(df['period'].unique())
        terminals = sorted(df['terminal_id'].unique())

        # Create heatmap data
        transaction_volume = []
        transaction_count = []
        average_transaction_value = []

        for terminal in terminals:
            terminal_data = df[df['terminal_id'] == terminal]

            # Volume data
            volume_row = {'terminal': terminal, 'terminal_id': terminal}
            count_row = {'terminal': terminal, 'terminal_id': terminal}
            avg_row = {'terminal': terminal, 'terminal_id': terminal}

            for period in periods:
                period_data = terminal_data[terminal_data['period'] == period]

                volume = period_data['amount'].sum()
                count = len(period_data)
                avg = period_data['amount'].mean() if len(period_data) > 0 else 0

                volume_row[period] = round(volume, 2)
                count_row[period] = count
                avg_row[period] = round(avg, 2) if avg > 0 else 0

            transaction_volume.append(volume_row)
            transaction_count.append(count_row)
            average_transaction_value.append(avg_row)

        return {
            "metric": f"Terminal Activity Heatmap for Branch {branch_admin_id}",
            "periods": periods,
            "transaction_volume": transaction_volume,
            "transaction_count": transaction_count,
            "average_transaction_value": average_transaction_value
        }

    except Exception as e:
        # Fallback to basic structure if there's an error
        return {
            "metric": f"Terminal Activity Heatmap for Branch {branch_admin_id}",
            "periods": [],
            "transaction_volume": [],
            "transaction_count": [],
            "average_transaction_value": []
        }


@router.get("/{branch_admin_id}/transaction-volume", response_model=GraphData)
def branch_admin_transaction_volume(
    branch_admin_id: str,
    granularity: str = Query(..., pattern="^(daily|weekly|monthly|yearly)$"),
    year: int = None,
    month: int = None,
    week: int = None,
    day: int = Query(None, ge=1, le=31),
    range_days: int = Query(None, ge=1),
    start_date: str = None,
    end_date: str = None,
    channel: str = None,
    df=Depends(get_df)
):
    # Use the helper function to filter data
    df, _ = filter_entity_data(
        df, "branch_admin_id", branch_admin_id,
        year, month, week, day, range_days, start_date, end_date, channel
    )

    return get_transaction_volume_over_time(df, granularity)


@router.get("/{branch_admin_id}/transaction-count", response_model=GraphData)
def branch_admin_transaction_count(
    branch_admin_id: str,
    granularity: str = Query(..., pattern="^(daily|weekly|monthly|yearly)$"),
    year: int = None,
    month: int = None,
    week: int = None,
    day: int = Query(None, ge=1, le=31),
    range_days: int = Query(None, ge=1),
    start_date: str = None,
    end_date: str = None,
    channel: str = None,
    df=Depends(get_df)
):
    # Use the helper function to filter data
    df, filters = filter_entity_data(
        df, "branch_admin_id", branch_admin_id,
        year, month, week, day, range_days, start_date, end_date, channel
    )

    return get_transaction_count_over_time(df, granularity, filters)


@router.get("/{branch_admin_id}/transaction-outliers", response_model=TableData)
def branch_admin_transaction_outliers(
    branch_admin_id: str,
    year: int = None,
    month: int = None,
    week: int = None,
    day: int = Query(None, ge=1, le=31),
    range_days: int = Query(None, ge=1),
    start_date: str = None,
    end_date: str = None,
    channel: str = None,
    df=Depends(get_df)
):
    # Use the helper function to filter data
    df, filters = filter_entity_data(
        df, "branch_admin_id", branch_admin_id,
        year, month, week, day, range_days, start_date, end_date, channel
    )

    return get_transaction_outliers(df, filters)


@router.get("/{branch_admin_id}/days-between-transactions", response_model=TableData)
def branch_admin_days_between_transactions(
    branch_admin_id: str,
    year: int = None,
    month: int = None,
    week: int = None,
    day: int = Query(None, ge=1, le=31),
    range_days: int = Query(None, ge=1),
    start_date: str = None,
    end_date: str = None,
    channel: str = None,
    df=Depends(get_df)
):
    # Use the helper function to filter data
    df, filters = filter_entity_data(
        df, "branch_admin_id", branch_admin_id,
        year, month, week, day, range_days, start_date, end_date, channel
    )

    return get_days_between_transactions(df, filters)


@router.get("/{branch_admin_id}/transaction-frequency-analysis", response_model=TableData)
def branch_admin_transaction_frequency_analysis(
    branch_admin_id: str,
    year: int = None,
    month: int = None,
    week: int = None,
    day: int = Query(None, ge=1, le=31),
    range_days: int = Query(None, ge=1),
    start_date: str = None,
    end_date: str = None,
    channel: str = None,
    df=Depends(get_df)
):
    """
    Analyze transaction frequency patterns for a specific branch.
    Returns day of week patterns, hour of day patterns, and overall activity metrics.
    """
    # Use the helper function to filter data
    df, filters = filter_entity_data(
        df, "branch_admin_id", branch_admin_id,
        year, month, week, day, range_days, start_date, end_date, channel
    )

    return get_transaction_frequency_analysis(df, filters)


@router.get("/{branch_admin_id}/export")
def export_branch_admin_data(
    branch_admin_id: str,
    year: int = None,
    month: int = None,
    week: int = None,
    day: int = Query(None, ge=1, le=31),
    range_days: int = Query(None, ge=1),
    start_date: str = None,
    end_date: str = None,
    channel: str = None,
    df=Depends(get_df)
):
    # Use the helper function to filter data
    df, _ = filter_entity_data(
        df, "branch_admin_id", branch_admin_id,
        year, month, week, day, range_days, start_date, end_date, channel
    )
    
    # Convert to CSV
    output = StringIO()
    df.to_csv(output, index=False)
    output.seek(0)
    
    # Return as downloadable file
    return StreamingResponse(
        iter([output.getvalue()]),
        media_type="text/csv",
        headers={"Content-Disposition": f"attachment; filename=branch_admin_{branch_admin_id}_data.csv"}
    )


@router.post("/filter", response_model=List[Dict[str, Any]])
def filter_branch_admins(
    filter_structure: Dict[str, Any] = Body(...),
    df=Depends(get_df)
):
    """
    Filter branch admins based on complex filter criteria.
    
    The filter_structure should follow the format:
    {
        "and": [
            {"column": "total_transactions", "operator": "greater_than", "value": 30},
            {"column": "unique_terminals", "operator": "greater_than", "value": 5}
        ]
    }
    
    Supported operators: equals, greater_than, less_than, between, in
    """
    try:
        # Add computed attributes with branch_admin_id as the grouping column
        filtered_df = filter_transactions(df, filter_structure, id_col='branch_admin_id')
        
        if filtered_df.empty:
            return []
        
        # Get unique branch admins with their attributes
        branch_admin_cols = ['branch_admin_id', 'avg_transaction_amount', 'total_transactions', 'unique_terminals']
        available_cols = [col for col in branch_admin_cols if col in filtered_df.columns]
        
        branch_admins = filtered_df[available_cols].drop_duplicates('branch_admin_id').to_dict(orient='records')
        return branch_admins
        
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))


