#!/usr/bin/env python3
"""
Script to add branch_name field to transactions.csv with realistic Ghanaian branch names.
"""

import pandas as pd
import random
import numpy as np

# Realistic Ghanaian branch names
BRANCH_NAMES = [
    "Spintex",
    "Tema",
    "Tip Toe Lane", 
    "East Legon",
    "Osu",
    "Kumasi Central",
    "Takoradi Market",
    "Accra Mall",
    "Achimota",
    "Kaneshie",
    "Circle",
    "Dansoman",
    "Madina",
    "Kasoa",
    "Lapaz",
    "Nima",
    "Asylum Down",
    "Ridge",
    "Airport",
    "Cantonments",
    "Labone",
    "Roman Ridge",
    "North Ridge",
    "Dzorwulu",
    "Adabraka",
    "Tudu",
    "Makola",
    "Kejetia",
    "Adum",
    "Suame",
    "Bantama",
    "Asafo",
    "Tafo",
    "Ejisu",
    "Bekwai",
    "Obuasi",
    "Konongo",
    "Kumawu",
    "Mampong",
    "Techiman",
    "Sunyani",
    "Berekum",
    "Dormaa",
    "Wen<PERSON>",
    "Kintampo",
    "Tamale Central",
    "Bolgatanga",
    "Wa",
    "Navrongo",
    "Yendi",
    "Salaga",
    "Bawku",
    "Gushegu",
    "Karaga",
    "Tolon",
    "Kumbungu",
    "Savelugu",
    "Nalerigu",
    "Cape Coast",
    "Elmina",
    "Winneba",
    "Swedru",
    "Agona",
    "Dunkwa",
    "Tarkwa",
    "Prestea",
    "Bogoso",
    "Axim",
    "Half Assini",
    "Elubo",
    "Enchi",
    "Sefwi Bekwai",
    "Wiawso",
    "Bibiani",
    "Goaso",
    "Bechem",
    "Sampa",
    "Drobo",
    "Ho Central",
    "Kpando",
    "Hohoe",
    "Jasikan",
    "Kadjebi",
    "Nkwanta",
    "Dambai",
    "Kete Krachi",
    "Koforidua",
    "Nkawkaw",
    "Mpraeso",
    "Abetifi",
    "Kibi",
    "Begoro",
    "Suhum",
    "Nsawam",
    "Akim Oda",
    "Kade",
    "Akropong",
    "Somanya"
]

def assign_branch_names():
    """Assign branch names to transactions based on branch_admin_id"""
    print("Loading transactions.csv...")
    
    # Read the CSV file
    df = pd.read_csv('data/transactions.csv')
    
    print(f"Loaded {len(df)} transactions")
    print(f"Unique branch_admin_ids: {df['branch_admin_id'].nunique()}")
    
    # Set random seed for reproducibility
    random.seed(42)
    np.random.seed(42)
    
    # Get unique branch_admin_ids
    unique_branch_admins = df['branch_admin_id'].unique()
    
    # Create a mapping from branch_admin_id to branch_name
    # Each branch_admin_id gets a consistent branch name
    branch_mapping = {}
    
    for i, branch_admin_id in enumerate(unique_branch_admins):
        # Use modulo to cycle through branch names if we have more branch_admins than names
        branch_name = BRANCH_NAMES[i % len(BRANCH_NAMES)]
        branch_mapping[branch_admin_id] = branch_name
    
    print(f"Created mapping for {len(branch_mapping)} branch admins")
    print("Sample mappings:")
    for i, (admin_id, branch_name) in enumerate(list(branch_mapping.items())[:10]):
        print(f"  {admin_id} -> {branch_name}")
    
    # Apply the mapping to create branch_name column
    print("Applying branch name mapping...")
    df['branch_name'] = df['branch_admin_id'].map(branch_mapping)
    
    # Check for any missing mappings
    missing_count = df['branch_name'].isna().sum()
    if missing_count > 0:
        print(f"Warning: {missing_count} transactions have missing branch names")
    
    print(f"Sample branch names: {df['branch_name'].value_counts().head().to_dict()}")
    
    # Create backup of original file
    print("Creating backup of original file...")
    df_original = pd.read_csv('data/transactions.csv')
    df_original.to_csv('data/transactions_backup_before_branch_names.csv', index=False)
    
    # Save updated file
    print("Saving updated transactions.csv...")
    df.to_csv('data/transactions.csv', index=False)
    
    print("✅ Successfully added branch names to transactions.csv!")
    print(f"✅ Original file backed up as transactions_backup_before_branch_names.csv")
    print(f"✅ Updated {len(df)} transaction records")
    print(f"✅ Added {df['branch_name'].nunique()} unique branch names")

if __name__ == "__main__":
    assign_branch_names()
