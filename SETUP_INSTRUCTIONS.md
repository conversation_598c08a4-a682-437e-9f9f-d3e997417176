# RPAY Agent Dashboard Setup Instructions

## Overview
The RPAY dashboard has been successfully adapted to work with the backend agent analytics API. The dashboard now fetches real data from the backend and displays agent-specific metrics, charts, and insights.

## What's Been Implemented

### Backend Changes
1. **Added Agent Stats Endpoint**: `/agents/{agent_id}/stats` - Returns key metrics for dashboard cards
2. **Added Agent List Endpoint**: `/agents/list` - Returns list of all available agents
3. **Enhanced CORS**: Backend allows requests from the frontend
4. **Existing Endpoints Used**:
   - `/agents/{agent_id}/overview` - Comprehensive agent data
   - `/agents/{agent_id}/transaction-volume` - Transaction volume over time
   - `/agents/{agent_id}/top-customers` - Top customers by amount/count

### Frontend Changes
1. **API Service Layer**: 
   - `src/lib/api.ts` - Axios instance with base configuration
   - `src/services/agents.ts` - Agent API service functions
   - `src/types/api.ts` - TypeScript types for API responses

2. **React Query Hooks**: 
   - `src/hooks/use-agents.ts` - Data fetching hooks with caching

3. **Components**:
   - `src/components/agent-selector.tsx` - Agent selection dropdown
   - Updated `Overview` component to use real transaction volume data
   - Updated `RecentSales` component to show top customers
   - Added `TransactionCountChart` component for analytics tab

4. **Dashboard Features**:
   - Agent selection dropdown with real agent data from backend
   - Auto-selection of first agent when page loads
   - **Comprehensive Filtering System**:
     - Date filters (year, month, week, day, custom date range, last N days)
     - Time granularity selector (daily, weekly, monthly, yearly)
     - Top results mode (by amount or count) with configurable limit
     - Filter summary with individual filter removal
   - Real-time stats cards (Total Revenue, Customers, Transactions, etc.)
   - Transaction volume chart with configurable time periods
   - Top customers list with configurable sorting and limits
   - Transaction count analytics chart
   - Loading states and error handling

## Setup Instructions

### 1. Backend Setup
```bash
cd rpay-analytics-api

# Activate virtual environment
source .venv/bin/activate

# Install dependencies
pip install -r requirements.txt

# Start the server
uvicorn app.main:app --reload --host 0.0.0.0 --port 8000
```

### 2. Frontend Setup
```bash
cd rpay-dashboard

# Install dependencies
npm install
# or
pnpm install

# Create .env file (already created)
# VITE_API_BASE_URL=http://localhost:8000

# Start the development server
npm run dev
# or
pnpm dev
```

### 3. Data Requirements
The backend needs transaction data to work properly. Make sure you have:
- A CSV file with transaction data in the `rpay-analytics-api/data/` directory
- The CSV should contain columns: `agent_id`, `customer_id`, `merchant_id`, `terminal_id`, `amount`, etc.

### 4. Testing the Dashboard
1. Open the dashboard at `http://localhost:5173` (or your Vite dev server URL)
2. Select an agent from the dropdown in the top-right
3. **Test the new smart filtering system**:
   - **Quick Tab**: Try "This Year", "This Month", or select specific year/month
   - **Range Tab**: Use preset buttons (7, 30, 90 days) or enter custom days
   - **Custom Tab**: Pick specific start and end dates
   - **Advanced Tab**: Filter by week numbers or specific days
   - Change time granularity (daily, weekly, monthly, yearly)
   - Switch top customers sorting (by amount vs count)
   - Adjust result limits
4. View the real-time filtered data in:
   - Stats cards (Total Revenue, Customers, Transactions, Merchants)
   - Transaction Volume chart
   - Top Customers list
   - Analytics tab with Transaction Count chart
5. **Test filter management**:
   - View active filters in the filter summary
   - Remove individual filters
   - Clear all filters at once

## Features Implemented

### Dashboard Cards
- **Total Transaction Value**: Sum of all transactions for the agent (filtered)
- **Average Transaction Value**: Mean transaction amount (filtered)
- **Transaction Count**: Total number of transactions (filtered)
- **Unique Customers**: Number of distinct customers (filtered)
- **Unique Merchants**: Number of distinct merchants (filtered)
- **Unique Terminals**: Number of distinct terminals (filtered)

### Charts
- **Transaction Volume Over Time**: Configurable time period bar chart showing transaction amounts
- **Transaction Count Over Time**: Configurable time period line chart showing transaction counts

### Filtering System
- **Smart Date Filter Tabs**:
  - **Quick**: Year/month selection with "This Year" and "This Month" presets
  - **Range**: Last N days with common presets (7, 30, 90, 365 days)
  - **Custom**: Start and end date picker for specific date ranges
  - **Advanced**: Week numbers and day of month filters
- **Display Options**:
  - Time granularity (daily, weekly, monthly, yearly)
  - Top results sorting (by amount or transaction count)
  - Configurable result limits
- **Filter Management**:
  - Contextual filter tabs (only show relevant options)
  - Visual filter summary with active filters
  - Individual filter removal
  - Clear all filters option
  - Auto-switching between filter modes

### Data Display
- **Top Customers**: Configurable list with sorting and filtering options
- **Loading States**: Skeleton loaders while data is fetching
- **Error Handling**: Graceful error messages when API calls fail
- **Empty States**: Helpful messages when no agent is selected

## API Endpoints Used

1. `GET /agents/list` - List of all available agents
2. `GET /agents/{agent_id}/stats` - Dashboard stats cards
3. `GET /agents/{agent_id}/overview` - Comprehensive overview data
4. `GET /agents/{agent_id}/transaction-volume?granularity=monthly` - Volume chart
5. `GET /agents/{agent_id}/transaction-count?granularity=monthly` - Count chart
6. `GET /agents/{agent_id}/top-customers?mode=amount&limit=5` - Top customers

## Environment Variables

### Frontend (.env)
```
VITE_API_BASE_URL=http://localhost:8000
```

## Next Steps

### Potential Enhancements
1. **Date Filtering**: Add date range pickers for filtering data
3. **More Charts**: Add customer segmentation, merchant analysis, etc.
4. **Export Functionality**: Implement CSV/PDF export for reports
5. **Real-time Updates**: Add WebSocket support for live data updates
6. **Caching**: Implement better caching strategies for performance

### Additional Analytics
The backend already supports many more endpoints that could be integrated:
- Customer segmentation
- Merchant segmentation  
- Transaction outliers
- Days between transactions
- Merchant activity heatmap

## Troubleshooting

### Quick Fixes
1. **500 Errors**: Check if backend is running and has data loaded
2. **Connection Issues**: Verify `VITE_API_BASE_URL` in .env file
3. **No Agents**: Ensure transaction data CSV has agent_id column
4. **Filter Errors**: Try clearing all filters and refreshing

### Detailed Troubleshooting
For comprehensive troubleshooting, see **[TROUBLESHOOTING.md](./TROUBLESHOOTING.md)** which includes:
- Step-by-step solutions for common issues
- Debug scripts and tools
- Environment setup checklist
- Log collection guidance

### Debug Tools
```bash
# Test backend connection and data
python debug_backend.py

# Test filtering functionality
python test_filters.py

# Test agent list specifically
python test_agents_list.py
```
