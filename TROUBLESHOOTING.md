# RPAY Dashboard Troubleshooting Guide

## Common Issues and Solutions

### 1. 500 Error on Dashboard Load

**Symptoms:**
- Dashboard shows "Connection Error" 
- Browser console shows 500 errors
- Backend returns internal server errors

**Causes & Solutions:**

#### A. Backend Not Running
```bash
# Check if backend is running
curl http://localhost:8000/docs

# If not running, start it:
cd rpay-analytics-api
source .venv/bin/activate
uvicorn app.main:app --reload --host 0.0.0.0 --port 8000
```

#### B. No Transaction Data Loaded
The backend needs CSV data to work. Check if you have data:
```bash
# Test the debug script
python debug_backend.py

# If no agents found, you need to load transaction data
# Make sure you have a CSV file in rpay-analytics-api/data/
```

#### C. Database/CSV Issues
```bash
# Check backend logs for specific errors
# Look for pandas errors, file not found, or column issues
```

### 2. Select Component Errors

**Error:** `A <Select.Item /> must have a value prop that is not an empty string`

**Solution:** ✅ Fixed in latest version
- Updated date filters to use 'all' instead of empty strings
- All Select components now have proper non-empty values

### 3. Agent Selector Issues

**Symptoms:**
- Agent dropdown doesn't load
- Shows "Error loading agents"
- No agents available

**Solutions:**

#### Check Backend Agent Endpoint
```bash
# Test agents list endpoint
curl http://localhost:8000/agents/list

# Should return array of agents like:
# [{"id": "AGT001", "name": "Agent AGT001"}, ...]
```

#### Verify Transaction Data
```bash
# Check if your CSV has agent_id column
# Make sure agent_id values are not null/empty
```

### 4. CORS Errors

**Symptoms:**
- Browser console shows CORS errors
- Network requests blocked

**Solutions:**

#### Check API Base URL
```bash
# In rpay-dashboard/.env
VITE_API_BASE_URL=http://localhost:8000

# Make sure this matches your backend URL
```

#### Backend CORS Configuration
The backend should already have CORS enabled for localhost.

### 5. Chart/Data Loading Issues

**Symptoms:**
- Charts show "Loading..." indefinitely
- "Error loading chart data" messages
- Empty data responses

**Solutions:**

#### Test Specific Endpoints
```bash
# Test with a known agent ID
python test_filters.py

# Or manually test:
curl "http://localhost:8000/agents/AGT001/stats"
curl "http://localhost:8000/agents/AGT001/transaction-volume?granularity=monthly"
```

#### Check Date Filters
- Try clearing all filters
- Test with simple filters first (just year)
- Verify date ranges are valid

### 6. Filter-Related Errors

**Symptoms:**
- Filters don't apply
- Error when changing filters
- Unexpected filter behavior
- Stats cards don't change when filters are applied

**Solutions:**

#### Verify Filtering is Working
```bash
# Test filtering with the frontend test script
python test_filtering_frontend.py

# This will test the exact API calls the dashboard makes
# and show if different filters return different values
```

#### Check Debug Panel (Development Mode)
- Look for the orange "Debug Panel" in the dashboard
- Verify that filter parameters are being sent correctly
- Check that API responses change when filters change

#### Clear Browser Cache
- Hard refresh (Ctrl+F5 or Cmd+Shift+R)
- Clear browser cache and cookies

#### Reset Filters
- Use "Clear all" button in filter summary
- Refresh the page

#### Check Filter Values
- Ensure date ranges are valid
- Check that numeric inputs are reasonable
- Verify granularity matches data availability

#### Verify Data Coverage
- Make sure your transaction data covers the filtered periods
- Test with broader date ranges first (e.g., current year)
- Check that agent has transactions in the filtered period

## Debug Tools

### 1. Backend Debug Script
```bash
python debug_backend.py
```
Tests all major endpoints and shows detailed error information.

### 2. Filter Test Script
```bash
python test_filters.py
```
Tests various filter combinations to ensure backend handles them correctly.

### 3. Agent List Test
```bash
python test_agents_list.py
```
Specifically tests the agent list endpoint.

### 4. Frontend Filtering Test
```bash
python test_filtering_frontend.py
```
Tests the exact API calls the dashboard makes with different filters to verify filtering is working correctly.

### 4. Browser Developer Tools
- **Console:** Check for JavaScript errors and API call failures
- **Network:** Monitor API requests and responses
- **React DevTools:** Inspect component state and props

## Environment Setup Checklist

### Backend Requirements
- [ ] Python virtual environment activated
- [ ] All dependencies installed (`pip install -r requirements.txt`)
- [ ] Transaction data CSV file available
- [ ] Backend server running on port 8000
- [ ] No errors in backend logs

### Frontend Requirements
- [ ] Node.js dependencies installed (`npm install`)
- [ ] `.env` file with correct API URL
- [ ] Frontend dev server running
- [ ] No build errors in console

### Data Requirements
- [ ] CSV file has required columns (agent_id, customer_id, merchant_id, amount, etc.)
- [ ] Agent IDs are not null/empty
- [ ] Date columns are properly formatted
- [ ] Numeric columns contain valid numbers

## Getting Help

### Log Collection
When reporting issues, include:

1. **Backend logs** (from uvicorn output)
2. **Browser console errors** (F12 → Console)
3. **Network tab** showing failed requests
4. **Output from debug scripts**

### Common Log Patterns

#### Backend Issues
```
# File not found
FileNotFoundError: [Errno 2] No such file or directory: 'data/transactions.csv'

# Column missing
KeyError: 'agent_id'

# Data type issues
ValueError: cannot convert float NaN to integer
```

#### Frontend Issues
```
# API connection
Network Error: Request failed with status code 500

# Component errors
Error: Cannot read property 'map' of undefined

# CORS issues
Access to XMLHttpRequest blocked by CORS policy
```

### Quick Fixes

1. **Restart everything:**
   ```bash
   # Backend
   cd rpay-analytics-api && uvicorn app.main:app --reload
   
   # Frontend
   cd rpay-dashboard && npm run dev
   ```

2. **Clear all caches:**
   - Browser cache (Ctrl+Shift+Delete)
   - React Query cache (refresh page)
   - Backend cache (restart server)

3. **Reset to defaults:**
   - Clear all dashboard filters
   - Select a different agent
   - Use simple date ranges first
