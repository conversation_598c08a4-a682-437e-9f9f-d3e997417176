#!/usr/bin/env python3
"""
Test script to verify that filtering is working correctly by testing the exact
API calls that the frontend would make with different filter combinations.
"""

import requests
import json
from datetime import datetime, timedelta

BASE_URL = "http://localhost:8000"

def test_agent_stats_with_filters(agent_id: str):
    """Test agent stats endpoint with various filter combinations"""
    
    print(f"\n🧪 Testing Agent Stats Filtering for Agent: {agent_id}")
    print("=" * 60)
    
    # Test cases that match what the frontend would send
    test_cases = [
        {
            "name": "No Filters (All Time)",
            "params": {},
            "expected": "Should return all-time stats"
        },
        {
            "name": "Current Year Filter",
            "params": {"year": datetime.now().year},
            "expected": "Should return stats for current year only"
        },
        {
            "name": "Specific Month",
            "params": {"year": datetime.now().year, "month": datetime.now().month},
            "expected": "Should return stats for current month only"
        },
        {
            "name": "Last 30 Days",
            "params": {"range_days": 30},
            "expected": "Should return stats for last 30 days"
        },
        {
            "name": "Last 7 Days",
            "params": {"range_days": 7},
            "expected": "Should return stats for last 7 days"
        },
        {
            "name": "Custom Date Range",
            "params": {
                "start_date": (datetime.now() - timedelta(days=60)).strftime("%Y-%m-%d"),
                "end_date": (datetime.now() - timedelta(days=30)).strftime("%Y-%m-%d")
            },
            "expected": "Should return stats for 60-30 days ago"
        }
    ]
    
    results = []
    
    for test_case in test_cases:
        print(f"\n📊 {test_case['name']}")
        print(f"   Params: {test_case['params']}")
        print(f"   Expected: {test_case['expected']}")
        
        try:
            response = requests.get(
                f"{BASE_URL}/agents/{agent_id}/stats",
                params=test_case['params'],
                timeout=10
            )
            
            if response.status_code == 200:
                data = response.json()
                print(f"   ✅ Success - Got {len(data)} stats")
                
                # Show key metrics to verify filtering
                for stat in data:
                    if 'Transaction Value' in stat['metric']:
                        print(f"      💰 {stat['metric']}: ${stat['value']:,.2f}")
                    elif 'Transaction Count' in stat['metric']:
                        print(f"      📈 {stat['metric']}: {stat['value']:,}")
                
                results.append({
                    "test": test_case['name'],
                    "success": True,
                    "data": data
                })
            else:
                print(f"   ❌ Error {response.status_code}: {response.text}")
                results.append({
                    "test": test_case['name'],
                    "success": False,
                    "error": response.text
                })
                
        except Exception as e:
            print(f"   ❌ Exception: {str(e)}")
            results.append({
                "test": test_case['name'],
                "success": False,
                "error": str(e)
            })
    
    return results

def compare_filter_results(results):
    """Compare results to verify filtering is working"""
    print(f"\n🔍 Filter Comparison Analysis")
    print("=" * 60)
    
    successful_results = [r for r in results if r['success']]
    
    if len(successful_results) < 2:
        print("❌ Not enough successful results to compare")
        return
    
    # Compare transaction values between different filters
    all_time = next((r for r in successful_results if 'No Filters' in r['test']), None)
    last_30_days = next((r for r in successful_results if 'Last 30 Days' in r['test']), None)
    current_year = next((r for r in successful_results if 'Current Year' in r['test']), None)
    
    def get_transaction_value(result):
        if not result or not result['data']:
            return None
        for stat in result['data']:
            if 'Total Transaction Value' in stat['metric']:
                return stat['value']
        return None
    
    def get_transaction_count(result):
        if not result or not result['data']:
            return None
        for stat in result['data']:
            if 'Transaction Count' in stat['metric']:
                return stat['value']
        return None
    
    print("\n📈 Transaction Value Comparison:")
    if all_time:
        all_time_value = get_transaction_value(all_time)
        print(f"   All Time: ${all_time_value:,.2f}" if all_time_value else "   All Time: No data")
    
    if current_year:
        current_year_value = get_transaction_value(current_year)
        print(f"   Current Year: ${current_year_value:,.2f}" if current_year_value else "   Current Year: No data")
    
    if last_30_days:
        last_30_value = get_transaction_value(last_30_days)
        print(f"   Last 30 Days: ${last_30_value:,.2f}" if last_30_value else "   Last 30 Days: No data")
    
    # Verify logical relationships
    print(f"\n🧮 Logical Checks:")
    
    if all_time and current_year:
        all_time_val = get_transaction_value(all_time)
        current_year_val = get_transaction_value(current_year)
        if all_time_val and current_year_val:
            if all_time_val >= current_year_val:
                print("   ✅ All time >= Current year (correct)")
            else:
                print("   ⚠️  All time < Current year (unexpected)")
    
    if current_year and last_30_days:
        current_year_val = get_transaction_value(current_year)
        last_30_val = get_transaction_value(last_30_days)
        if current_year_val and last_30_val:
            if current_year_val >= last_30_val:
                print("   ✅ Current year >= Last 30 days (correct)")
            else:
                print("   ⚠️  Current year < Last 30 days (unexpected)")

def main():
    print("🚀 Frontend Filtering Test Suite")
    print("Testing the exact API calls that the dashboard makes")
    
    # Get first agent
    try:
        response = requests.get(f"{BASE_URL}/agents/list", timeout=10)
        if response.status_code != 200:
            print(f"❌ Cannot get agents list: {response.status_code}")
            return
        
        agents = response.json()
        if not agents:
            print("❌ No agents found")
            return
        
        agent_id = agents[0]['id']
        print(f"📋 Using agent: {agent_id}")
        
    except Exception as e:
        print(f"❌ Error getting agents: {str(e)}")
        return
    
    # Test filtering
    results = test_agent_stats_with_filters(agent_id)
    
    # Analyze results
    compare_filter_results(results)
    
    # Summary
    successful_tests = len([r for r in results if r['success']])
    total_tests = len(results)
    
    print(f"\n🏁 Test Summary")
    print("=" * 60)
    print(f"✅ Successful tests: {successful_tests}/{total_tests}")
    
    if successful_tests == total_tests:
        print("🎉 All filtering tests passed!")
        print("✅ The backend is correctly applying date filters to stats")
        print("✅ The frontend should show different values when filters change")
    else:
        print("⚠️  Some filtering tests failed")
        print("💡 Check backend logs and ensure transaction data covers the test periods")
    
    print(f"\n💡 Next Steps:")
    print("1. Open the dashboard and apply different filters")
    print("2. Check that the stats cards show different values")
    print("3. Use the debug panel (in dev mode) to verify API parameters")
    print("4. Compare dashboard values with the test results above")

if __name__ == "__main__":
    main()
