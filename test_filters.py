#!/usr/bin/env python3
"""
Test script to verify filtering functionality works with the backend
"""

import requests
import json
from datetime import datetime, timedelta

BASE_URL = "http://localhost:8000"

def test_filtered_endpoint(endpoint: str, params: dict, description: str):
    """Test an endpoint with filter parameters"""
    url = f"{BASE_URL}{endpoint}"
    print(f"\n🧪 Testing: {description}")
    print(f"📍 URL: {url}")
    print(f"🔧 Params: {params}")
    
    try:
        response = requests.get(url, params=params, timeout=10)
        print(f"✅ Status: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            if isinstance(data, dict) and 'data' in data:
                # For GraphData/TableData responses
                if 'labels' in data['data']:
                    print(f"📊 Data points: {len(data['data']['labels'])}")
                elif isinstance(data['data'], list):
                    print(f"📊 Records: {len(data['data'])}")
            elif isinstance(data, list):
                # For SimpleStat responses
                print(f"📊 Stats returned: {len(data)}")
                for stat in data[:3]:  # Show first 3 stats
                    print(f"   - {stat['metric']}: {stat['value']}")
            return {"success": True, "data": data}
        else:
            print(f"❌ Error: {response.text}")
            return {"success": False, "error": response.text}
            
    except requests.exceptions.ConnectionError:
        print("❌ Connection Error: Is the backend server running?")
        return {"success": False, "error": "Connection failed"}
    except Exception as e:
        print(f"❌ Error: {str(e)}")
        return {"success": False, "error": str(e)}

def main():
    print("🚀 RPAY Analytics API Filter Test Suite")
    print("=" * 60)
    
    # Get agent list first
    agents_result = test_filtered_endpoint("/agents/list", {}, "Get Agent List")
    
    if not agents_result.get("success") or not agents_result.get("data"):
        print("❌ Cannot proceed without agent data")
        return
    
    agents = agents_result["data"]
    if len(agents) == 0:
        print("❌ No agents found in the system")
        return
    
    sample_agent_id = agents[0]["id"]
    print(f"\n📋 Using agent: {sample_agent_id}")
    
    # Test various filter combinations
    current_year = datetime.now().year
    current_month = datetime.now().month
    
    test_cases = [
        # Basic stats with different time filters
        {
            "endpoint": f"/agents/{sample_agent_id}/stats",
            "params": {"year": current_year},
            "description": f"Agent Stats - Current Year ({current_year})"
        },
        {
            "endpoint": f"/agents/{sample_agent_id}/stats",
            "params": {"year": current_year, "month": current_month},
            "description": f"Agent Stats - Current Month ({current_year}-{current_month:02d})"
        },
        {
            "endpoint": f"/agents/{sample_agent_id}/stats",
            "params": {"range_days": 30},
            "description": "Agent Stats - Last 30 Days"
        },
        
        # Transaction volume with different granularities
        {
            "endpoint": f"/agents/{sample_agent_id}/transaction-volume",
            "params": {"granularity": "monthly", "year": current_year},
            "description": "Transaction Volume - Monthly, Current Year"
        },
        {
            "endpoint": f"/agents/{sample_agent_id}/transaction-volume",
            "params": {"granularity": "daily", "range_days": 7},
            "description": "Transaction Volume - Daily, Last 7 Days"
        },
        
        # Top customers with different modes and limits
        {
            "endpoint": f"/agents/{sample_agent_id}/top-customers",
            "params": {"mode": "amount", "limit": 3, "year": current_year},
            "description": "Top 3 Customers by Amount - Current Year"
        },
        {
            "endpoint": f"/agents/{sample_agent_id}/top-customers",
            "params": {"mode": "count", "limit": 5, "range_days": 90},
            "description": "Top 5 Customers by Count - Last 90 Days"
        },
        
        # Custom date range (last 30 days)
        {
            "endpoint": f"/agents/{sample_agent_id}/overview",
            "params": {
                "granularity": "weekly",
                "start_date": (datetime.now() - timedelta(days=30)).strftime("%Y-%m-%d"),
                "end_date": datetime.now().strftime("%Y-%m-%d")
            },
            "description": "Overview - Weekly, Last 30 Days (Custom Range)"
        }
    ]
    
    success_count = 0
    for test_case in test_cases:
        result = test_filtered_endpoint(
            test_case["endpoint"],
            test_case["params"],
            test_case["description"]
        )
        if result.get("success"):
            success_count += 1
    
    print("\n" + "=" * 60)
    print(f"🏁 Test completed! {success_count}/{len(test_cases)} tests passed")
    
    if success_count == len(test_cases):
        print("🎉 All filter tests passed! The filtering system is working correctly.")
    else:
        print("⚠️  Some tests failed. Check the backend logs for details.")
    
    print("\n💡 Filter Parameters Tested:")
    print("- year, month: Specific time periods")
    print("- range_days: Last N days")
    print("- start_date, end_date: Custom date ranges")
    print("- granularity: daily, weekly, monthly")
    print("- mode: amount vs count sorting")
    print("- limit: Number of results")

if __name__ == "__main__":
    main()
