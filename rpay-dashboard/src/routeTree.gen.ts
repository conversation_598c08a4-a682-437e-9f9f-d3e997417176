/* eslint-disable */

// @ts-nocheck

// noinspection JSUnusedGlobalSymbols

// This file was automatically generated by TanStack Router.
// You should NOT make any changes in this file as it will be overwritten.
// Additionally, you should also exclude this file from your linter and/or formatter to prevent it from being checked or modified.

// Import Routes

import { Route as rootRoute } from './routes/__root'
import { Route as ClerkRouteImport } from './routes/clerk/route'
import { Route as AuthenticatedRouteImport } from './routes/_authenticated/route'
import { Route as AuthenticatedIndexImport } from './routes/_authenticated/index'
import { Route as errors503Import } from './routes/(errors)/503'
import { Route as errors500Import } from './routes/(errors)/500'
import { Route as errors404Import } from './routes/(errors)/404'
import { Route as errors403Import } from './routes/(errors)/403'
import { Route as errors401Import } from './routes/(errors)/401'
import { Route as authSignUpImport } from './routes/(auth)/sign-up'
import { Route as authSignIn2Import } from './routes/(auth)/sign-in-2'
import { Route as authSignInImport } from './routes/(auth)/sign-in'
import { Route as authOtpImport } from './routes/(auth)/otp'
import { Route as authForgotPasswordImport } from './routes/(auth)/forgot-password'
import { Route as ClerkAuthenticatedRouteImport } from './routes/clerk/_authenticated/route'
import { Route as ClerkauthRouteImport } from './routes/clerk/(auth)/route'
import { Route as AuthenticatedSettingsRouteImport } from './routes/_authenticated/settings/route'
import { Route as AuthenticatedMerchantsRouteImport } from './routes/_authenticated/merchants/route'
import { Route as AuthenticatedCustomersRouteImport } from './routes/_authenticated/customers/route'
import { Route as AuthenticatedBranchAdminsRouteImport } from './routes/_authenticated/branch-admins/route'
import { Route as AuthenticatedAgentsRouteImport } from './routes/_authenticated/agents/route'
import { Route as AuthenticatedTargetDiscoveryIndexImport } from './routes/_authenticated/target-discovery/index'
import { Route as AuthenticatedSettingsIndexImport } from './routes/_authenticated/settings/index'
import { Route as AuthenticatedMerchantsIndexImport } from './routes/_authenticated/merchants/index'
import { Route as AuthenticatedHelpCenterIndexImport } from './routes/_authenticated/help-center/index'
import { Route as AuthenticatedCustomersIndexImport } from './routes/_authenticated/customers/index'
import { Route as ClerkAuthenticatedUserManagementImport } from './routes/clerk/_authenticated/user-management'
import { Route as ClerkauthSignUpImport } from './routes/clerk/(auth)/sign-up'
import { Route as ClerkauthSignInImport } from './routes/clerk/(auth)/sign-in'
import { Route as AuthenticatedSettingsNotificationsImport } from './routes/_authenticated/settings/notifications'
import { Route as AuthenticatedSettingsDisplayImport } from './routes/_authenticated/settings/display'
import { Route as AuthenticatedSettingsAppearanceImport } from './routes/_authenticated/settings/appearance'
import { Route as AuthenticatedSettingsAccountImport } from './routes/_authenticated/settings/account'
import { Route as AuthenticatedMerchantsMerchantIdImport } from './routes/_authenticated/merchants/$merchantId'
import { Route as AuthenticatedBranchAdminsBranchIdImport } from './routes/_authenticated/branch-admins/$branchId'
import { Route as AuthenticatedAgentsAgentIdRouteImport } from './routes/_authenticated/agents/$agentId/route'
import { Route as AuthenticatedTargetDiscoveryMerchantsIndexImport } from './routes/_authenticated/target-discovery/merchants/index'
import { Route as AuthenticatedTargetDiscoveryCustomersIndexImport } from './routes/_authenticated/target-discovery/customers/index'
import { Route as AuthenticatedMerchantsMerchantIdIndexImport } from './routes/_authenticated/merchants/$merchantId/index'
import { Route as AuthenticatedTargetDiscoveryMerchantsSearchImport } from './routes/_authenticated/target-discovery/merchants/search'
import { Route as AuthenticatedTargetDiscoveryCustomersSearchImport } from './routes/_authenticated/target-discovery/customers/search'
import { Route as AuthenticatedMerchantsMerchantIdBranchesImport } from './routes/_authenticated/merchants/$merchantId/branches'
import { Route as AuthenticatedMerchantsMerchantIdBranchIdImport } from './routes/_authenticated/merchants/$merchantId/$branchId'
import { Route as AuthenticatedBranchAdminsBranchIdTerminalsImport } from './routes/_authenticated/branch-admins/$branchId/terminals'
import { Route as AuthenticatedMerchantsMerchantIdBranchIdIndexImport } from './routes/_authenticated/merchants/$merchantId/$branchId/index'
import { Route as AuthenticatedMerchantsMerchantIdBranchIdTerminalIdImport } from './routes/_authenticated/merchants/$merchantId/$branchId/$terminalId'

// Create/Update Routes

const ClerkRouteRoute = ClerkRouteImport.update({
  id: '/clerk',
  path: '/clerk',
  getParentRoute: () => rootRoute,
} as any)

const AuthenticatedRouteRoute = AuthenticatedRouteImport.update({
  id: '/_authenticated',
  getParentRoute: () => rootRoute,
} as any)

const AuthenticatedIndexRoute = AuthenticatedIndexImport.update({
  id: '/',
  path: '/',
  getParentRoute: () => AuthenticatedRouteRoute,
} as any)

const errors503Route = errors503Import.update({
  id: '/(errors)/503',
  path: '/503',
  getParentRoute: () => rootRoute,
} as any)

const errors500Route = errors500Import.update({
  id: '/(errors)/500',
  path: '/500',
  getParentRoute: () => rootRoute,
} as any)

const errors404Route = errors404Import.update({
  id: '/(errors)/404',
  path: '/404',
  getParentRoute: () => rootRoute,
} as any)

const errors403Route = errors403Import.update({
  id: '/(errors)/403',
  path: '/403',
  getParentRoute: () => rootRoute,
} as any)

const errors401Route = errors401Import.update({
  id: '/(errors)/401',
  path: '/401',
  getParentRoute: () => rootRoute,
} as any)

const authSignUpRoute = authSignUpImport.update({
  id: '/(auth)/sign-up',
  path: '/sign-up',
  getParentRoute: () => rootRoute,
} as any)

const authSignIn2Route = authSignIn2Import.update({
  id: '/(auth)/sign-in-2',
  path: '/sign-in-2',
  getParentRoute: () => rootRoute,
} as any)

const authSignInRoute = authSignInImport.update({
  id: '/(auth)/sign-in',
  path: '/sign-in',
  getParentRoute: () => rootRoute,
} as any)

const authOtpRoute = authOtpImport.update({
  id: '/(auth)/otp',
  path: '/otp',
  getParentRoute: () => rootRoute,
} as any)

const authForgotPasswordRoute = authForgotPasswordImport.update({
  id: '/(auth)/forgot-password',
  path: '/forgot-password',
  getParentRoute: () => rootRoute,
} as any)

const ClerkAuthenticatedRouteRoute = ClerkAuthenticatedRouteImport.update({
  id: '/_authenticated',
  getParentRoute: () => ClerkRouteRoute,
} as any)

const ClerkauthRouteRoute = ClerkauthRouteImport.update({
  id: '/(auth)',
  getParentRoute: () => ClerkRouteRoute,
} as any)

const AuthenticatedSettingsRouteRoute = AuthenticatedSettingsRouteImport.update(
  {
    id: '/settings',
    path: '/settings',
    getParentRoute: () => AuthenticatedRouteRoute,
  } as any,
)

const AuthenticatedMerchantsRouteRoute =
  AuthenticatedMerchantsRouteImport.update({
    id: '/merchants',
    path: '/merchants',
    getParentRoute: () => AuthenticatedRouteRoute,
  } as any)

const AuthenticatedCustomersRouteRoute =
  AuthenticatedCustomersRouteImport.update({
    id: '/customers',
    path: '/customers',
    getParentRoute: () => AuthenticatedRouteRoute,
  } as any)

const AuthenticatedBranchAdminsRouteRoute =
  AuthenticatedBranchAdminsRouteImport.update({
    id: '/branch-admins',
    path: '/branch-admins',
    getParentRoute: () => AuthenticatedRouteRoute,
  } as any)

const AuthenticatedAgentsRouteRoute = AuthenticatedAgentsRouteImport.update({
  id: '/agents',
  path: '/agents',
  getParentRoute: () => AuthenticatedRouteRoute,
} as any)

const AuthenticatedTargetDiscoveryIndexRoute =
  AuthenticatedTargetDiscoveryIndexImport.update({
    id: '/target-discovery/',
    path: '/target-discovery/',
    getParentRoute: () => AuthenticatedRouteRoute,
  } as any)

const AuthenticatedSettingsIndexRoute = AuthenticatedSettingsIndexImport.update(
  {
    id: '/',
    path: '/',
    getParentRoute: () => AuthenticatedSettingsRouteRoute,
  } as any,
)

const AuthenticatedMerchantsIndexRoute =
  AuthenticatedMerchantsIndexImport.update({
    id: '/',
    path: '/',
    getParentRoute: () => AuthenticatedMerchantsRouteRoute,
  } as any)

const AuthenticatedHelpCenterIndexRoute =
  AuthenticatedHelpCenterIndexImport.update({
    id: '/help-center/',
    path: '/help-center/',
    getParentRoute: () => AuthenticatedRouteRoute,
  } as any)

const AuthenticatedCustomersIndexRoute =
  AuthenticatedCustomersIndexImport.update({
    id: '/',
    path: '/',
    getParentRoute: () => AuthenticatedCustomersRouteRoute,
  } as any)

const ClerkAuthenticatedUserManagementRoute =
  ClerkAuthenticatedUserManagementImport.update({
    id: '/user-management',
    path: '/user-management',
    getParentRoute: () => ClerkAuthenticatedRouteRoute,
  } as any)

const ClerkauthSignUpRoute = ClerkauthSignUpImport.update({
  id: '/sign-up',
  path: '/sign-up',
  getParentRoute: () => ClerkauthRouteRoute,
} as any)

const ClerkauthSignInRoute = ClerkauthSignInImport.update({
  id: '/sign-in',
  path: '/sign-in',
  getParentRoute: () => ClerkauthRouteRoute,
} as any)

const AuthenticatedSettingsNotificationsRoute =
  AuthenticatedSettingsNotificationsImport.update({
    id: '/notifications',
    path: '/notifications',
    getParentRoute: () => AuthenticatedSettingsRouteRoute,
  } as any)

const AuthenticatedSettingsDisplayRoute =
  AuthenticatedSettingsDisplayImport.update({
    id: '/display',
    path: '/display',
    getParentRoute: () => AuthenticatedSettingsRouteRoute,
  } as any)

const AuthenticatedSettingsAppearanceRoute =
  AuthenticatedSettingsAppearanceImport.update({
    id: '/appearance',
    path: '/appearance',
    getParentRoute: () => AuthenticatedSettingsRouteRoute,
  } as any)

const AuthenticatedSettingsAccountRoute =
  AuthenticatedSettingsAccountImport.update({
    id: '/account',
    path: '/account',
    getParentRoute: () => AuthenticatedSettingsRouteRoute,
  } as any)

const AuthenticatedMerchantsMerchantIdRoute =
  AuthenticatedMerchantsMerchantIdImport.update({
    id: '/$merchantId',
    path: '/$merchantId',
    getParentRoute: () => AuthenticatedMerchantsRouteRoute,
  } as any)

const AuthenticatedBranchAdminsBranchIdRoute =
  AuthenticatedBranchAdminsBranchIdImport.update({
    id: '/$branchId',
    path: '/$branchId',
    getParentRoute: () => AuthenticatedBranchAdminsRouteRoute,
  } as any)

const AuthenticatedAgentsAgentIdRouteRoute =
  AuthenticatedAgentsAgentIdRouteImport.update({
    id: '/$agentId',
    path: '/$agentId',
    getParentRoute: () => AuthenticatedAgentsRouteRoute,
  } as any)

const AuthenticatedTargetDiscoveryMerchantsIndexRoute =
  AuthenticatedTargetDiscoveryMerchantsIndexImport.update({
    id: '/target-discovery/merchants/',
    path: '/target-discovery/merchants/',
    getParentRoute: () => AuthenticatedRouteRoute,
  } as any)

const AuthenticatedTargetDiscoveryCustomersIndexRoute =
  AuthenticatedTargetDiscoveryCustomersIndexImport.update({
    id: '/target-discovery/customers/',
    path: '/target-discovery/customers/',
    getParentRoute: () => AuthenticatedRouteRoute,
  } as any)

const AuthenticatedMerchantsMerchantIdIndexRoute =
  AuthenticatedMerchantsMerchantIdIndexImport.update({
    id: '/',
    path: '/',
    getParentRoute: () => AuthenticatedMerchantsMerchantIdRoute,
  } as any)

const AuthenticatedTargetDiscoveryMerchantsSearchRoute =
  AuthenticatedTargetDiscoveryMerchantsSearchImport.update({
    id: '/target-discovery/merchants/search',
    path: '/target-discovery/merchants/search',
    getParentRoute: () => AuthenticatedRouteRoute,
  } as any)

const AuthenticatedTargetDiscoveryCustomersSearchRoute =
  AuthenticatedTargetDiscoveryCustomersSearchImport.update({
    id: '/target-discovery/customers/search',
    path: '/target-discovery/customers/search',
    getParentRoute: () => AuthenticatedRouteRoute,
  } as any)

const AuthenticatedMerchantsMerchantIdBranchesRoute =
  AuthenticatedMerchantsMerchantIdBranchesImport.update({
    id: '/branches',
    path: '/branches',
    getParentRoute: () => AuthenticatedMerchantsMerchantIdRoute,
  } as any)

const AuthenticatedMerchantsMerchantIdBranchIdRoute =
  AuthenticatedMerchantsMerchantIdBranchIdImport.update({
    id: '/$branchId',
    path: '/$branchId',
    getParentRoute: () => AuthenticatedMerchantsMerchantIdRoute,
  } as any)

const AuthenticatedBranchAdminsBranchIdTerminalsRoute =
  AuthenticatedBranchAdminsBranchIdTerminalsImport.update({
    id: '/terminals',
    path: '/terminals',
    getParentRoute: () => AuthenticatedBranchAdminsBranchIdRoute,
  } as any)

const AuthenticatedMerchantsMerchantIdBranchIdIndexRoute =
  AuthenticatedMerchantsMerchantIdBranchIdIndexImport.update({
    id: '/',
    path: '/',
    getParentRoute: () => AuthenticatedMerchantsMerchantIdBranchIdRoute,
  } as any)

const AuthenticatedMerchantsMerchantIdBranchIdTerminalIdRoute =
  AuthenticatedMerchantsMerchantIdBranchIdTerminalIdImport.update({
    id: '/$terminalId',
    path: '/$terminalId',
    getParentRoute: () => AuthenticatedMerchantsMerchantIdBranchIdRoute,
  } as any)

// Populate the FileRoutesByPath interface

declare module '@tanstack/react-router' {
  interface FileRoutesByPath {
    '/_authenticated': {
      id: '/_authenticated'
      path: ''
      fullPath: ''
      preLoaderRoute: typeof AuthenticatedRouteImport
      parentRoute: typeof rootRoute
    }
    '/clerk': {
      id: '/clerk'
      path: '/clerk'
      fullPath: '/clerk'
      preLoaderRoute: typeof ClerkRouteImport
      parentRoute: typeof rootRoute
    }
    '/_authenticated/agents': {
      id: '/_authenticated/agents'
      path: '/agents'
      fullPath: '/agents'
      preLoaderRoute: typeof AuthenticatedAgentsRouteImport
      parentRoute: typeof AuthenticatedRouteImport
    }
    '/_authenticated/branch-admins': {
      id: '/_authenticated/branch-admins'
      path: '/branch-admins'
      fullPath: '/branch-admins'
      preLoaderRoute: typeof AuthenticatedBranchAdminsRouteImport
      parentRoute: typeof AuthenticatedRouteImport
    }
    '/_authenticated/customers': {
      id: '/_authenticated/customers'
      path: '/customers'
      fullPath: '/customers'
      preLoaderRoute: typeof AuthenticatedCustomersRouteImport
      parentRoute: typeof AuthenticatedRouteImport
    }
    '/_authenticated/merchants': {
      id: '/_authenticated/merchants'
      path: '/merchants'
      fullPath: '/merchants'
      preLoaderRoute: typeof AuthenticatedMerchantsRouteImport
      parentRoute: typeof AuthenticatedRouteImport
    }
    '/_authenticated/settings': {
      id: '/_authenticated/settings'
      path: '/settings'
      fullPath: '/settings'
      preLoaderRoute: typeof AuthenticatedSettingsRouteImport
      parentRoute: typeof AuthenticatedRouteImport
    }
    '/clerk/(auth)': {
      id: '/clerk/(auth)'
      path: '/'
      fullPath: '/clerk/'
      preLoaderRoute: typeof ClerkauthRouteImport
      parentRoute: typeof ClerkRouteImport
    }
    '/clerk/_authenticated': {
      id: '/clerk/_authenticated'
      path: ''
      fullPath: '/clerk'
      preLoaderRoute: typeof ClerkAuthenticatedRouteImport
      parentRoute: typeof ClerkRouteImport
    }
    '/(auth)/forgot-password': {
      id: '/(auth)/forgot-password'
      path: '/forgot-password'
      fullPath: '/forgot-password'
      preLoaderRoute: typeof authForgotPasswordImport
      parentRoute: typeof rootRoute
    }
    '/(auth)/otp': {
      id: '/(auth)/otp'
      path: '/otp'
      fullPath: '/otp'
      preLoaderRoute: typeof authOtpImport
      parentRoute: typeof rootRoute
    }
    '/(auth)/sign-in': {
      id: '/(auth)/sign-in'
      path: '/sign-in'
      fullPath: '/sign-in'
      preLoaderRoute: typeof authSignInImport
      parentRoute: typeof rootRoute
    }
    '/(auth)/sign-in-2': {
      id: '/(auth)/sign-in-2'
      path: '/sign-in-2'
      fullPath: '/sign-in-2'
      preLoaderRoute: typeof authSignIn2Import
      parentRoute: typeof rootRoute
    }
    '/(auth)/sign-up': {
      id: '/(auth)/sign-up'
      path: '/sign-up'
      fullPath: '/sign-up'
      preLoaderRoute: typeof authSignUpImport
      parentRoute: typeof rootRoute
    }
    '/(errors)/401': {
      id: '/(errors)/401'
      path: '/401'
      fullPath: '/401'
      preLoaderRoute: typeof errors401Import
      parentRoute: typeof rootRoute
    }
    '/(errors)/403': {
      id: '/(errors)/403'
      path: '/403'
      fullPath: '/403'
      preLoaderRoute: typeof errors403Import
      parentRoute: typeof rootRoute
    }
    '/(errors)/404': {
      id: '/(errors)/404'
      path: '/404'
      fullPath: '/404'
      preLoaderRoute: typeof errors404Import
      parentRoute: typeof rootRoute
    }
    '/(errors)/500': {
      id: '/(errors)/500'
      path: '/500'
      fullPath: '/500'
      preLoaderRoute: typeof errors500Import
      parentRoute: typeof rootRoute
    }
    '/(errors)/503': {
      id: '/(errors)/503'
      path: '/503'
      fullPath: '/503'
      preLoaderRoute: typeof errors503Import
      parentRoute: typeof rootRoute
    }
    '/_authenticated/': {
      id: '/_authenticated/'
      path: '/'
      fullPath: '/'
      preLoaderRoute: typeof AuthenticatedIndexImport
      parentRoute: typeof AuthenticatedRouteImport
    }
    '/_authenticated/agents/$agentId': {
      id: '/_authenticated/agents/$agentId'
      path: '/$agentId'
      fullPath: '/agents/$agentId'
      preLoaderRoute: typeof AuthenticatedAgentsAgentIdRouteImport
      parentRoute: typeof AuthenticatedAgentsRouteImport
    }
    '/_authenticated/branch-admins/$branchId': {
      id: '/_authenticated/branch-admins/$branchId'
      path: '/$branchId'
      fullPath: '/branch-admins/$branchId'
      preLoaderRoute: typeof AuthenticatedBranchAdminsBranchIdImport
      parentRoute: typeof AuthenticatedBranchAdminsRouteImport
    }
    '/_authenticated/merchants/$merchantId': {
      id: '/_authenticated/merchants/$merchantId'
      path: '/$merchantId'
      fullPath: '/merchants/$merchantId'
      preLoaderRoute: typeof AuthenticatedMerchantsMerchantIdImport
      parentRoute: typeof AuthenticatedMerchantsRouteImport
    }
    '/_authenticated/settings/account': {
      id: '/_authenticated/settings/account'
      path: '/account'
      fullPath: '/settings/account'
      preLoaderRoute: typeof AuthenticatedSettingsAccountImport
      parentRoute: typeof AuthenticatedSettingsRouteImport
    }
    '/_authenticated/settings/appearance': {
      id: '/_authenticated/settings/appearance'
      path: '/appearance'
      fullPath: '/settings/appearance'
      preLoaderRoute: typeof AuthenticatedSettingsAppearanceImport
      parentRoute: typeof AuthenticatedSettingsRouteImport
    }
    '/_authenticated/settings/display': {
      id: '/_authenticated/settings/display'
      path: '/display'
      fullPath: '/settings/display'
      preLoaderRoute: typeof AuthenticatedSettingsDisplayImport
      parentRoute: typeof AuthenticatedSettingsRouteImport
    }
    '/_authenticated/settings/notifications': {
      id: '/_authenticated/settings/notifications'
      path: '/notifications'
      fullPath: '/settings/notifications'
      preLoaderRoute: typeof AuthenticatedSettingsNotificationsImport
      parentRoute: typeof AuthenticatedSettingsRouteImport
    }
    '/clerk/(auth)/sign-in': {
      id: '/clerk/(auth)/sign-in'
      path: '/sign-in'
      fullPath: '/clerk/sign-in'
      preLoaderRoute: typeof ClerkauthSignInImport
      parentRoute: typeof ClerkauthRouteImport
    }
    '/clerk/(auth)/sign-up': {
      id: '/clerk/(auth)/sign-up'
      path: '/sign-up'
      fullPath: '/clerk/sign-up'
      preLoaderRoute: typeof ClerkauthSignUpImport
      parentRoute: typeof ClerkauthRouteImport
    }
    '/clerk/_authenticated/user-management': {
      id: '/clerk/_authenticated/user-management'
      path: '/user-management'
      fullPath: '/clerk/user-management'
      preLoaderRoute: typeof ClerkAuthenticatedUserManagementImport
      parentRoute: typeof ClerkAuthenticatedRouteImport
    }
    '/_authenticated/customers/': {
      id: '/_authenticated/customers/'
      path: '/'
      fullPath: '/customers/'
      preLoaderRoute: typeof AuthenticatedCustomersIndexImport
      parentRoute: typeof AuthenticatedCustomersRouteImport
    }
    '/_authenticated/help-center/': {
      id: '/_authenticated/help-center/'
      path: '/help-center'
      fullPath: '/help-center'
      preLoaderRoute: typeof AuthenticatedHelpCenterIndexImport
      parentRoute: typeof AuthenticatedRouteImport
    }
    '/_authenticated/merchants/': {
      id: '/_authenticated/merchants/'
      path: '/'
      fullPath: '/merchants/'
      preLoaderRoute: typeof AuthenticatedMerchantsIndexImport
      parentRoute: typeof AuthenticatedMerchantsRouteImport
    }
    '/_authenticated/settings/': {
      id: '/_authenticated/settings/'
      path: '/'
      fullPath: '/settings/'
      preLoaderRoute: typeof AuthenticatedSettingsIndexImport
      parentRoute: typeof AuthenticatedSettingsRouteImport
    }
    '/_authenticated/target-discovery/': {
      id: '/_authenticated/target-discovery/'
      path: '/target-discovery'
      fullPath: '/target-discovery'
      preLoaderRoute: typeof AuthenticatedTargetDiscoveryIndexImport
      parentRoute: typeof AuthenticatedRouteImport
    }
    '/_authenticated/branch-admins/$branchId/terminals': {
      id: '/_authenticated/branch-admins/$branchId/terminals'
      path: '/terminals'
      fullPath: '/branch-admins/$branchId/terminals'
      preLoaderRoute: typeof AuthenticatedBranchAdminsBranchIdTerminalsImport
      parentRoute: typeof AuthenticatedBranchAdminsBranchIdImport
    }
    '/_authenticated/merchants/$merchantId/$branchId': {
      id: '/_authenticated/merchants/$merchantId/$branchId'
      path: '/$branchId'
      fullPath: '/merchants/$merchantId/$branchId'
      preLoaderRoute: typeof AuthenticatedMerchantsMerchantIdBranchIdImport
      parentRoute: typeof AuthenticatedMerchantsMerchantIdImport
    }
    '/_authenticated/merchants/$merchantId/branches': {
      id: '/_authenticated/merchants/$merchantId/branches'
      path: '/branches'
      fullPath: '/merchants/$merchantId/branches'
      preLoaderRoute: typeof AuthenticatedMerchantsMerchantIdBranchesImport
      parentRoute: typeof AuthenticatedMerchantsMerchantIdImport
    }
    '/_authenticated/target-discovery/customers/search': {
      id: '/_authenticated/target-discovery/customers/search'
      path: '/target-discovery/customers/search'
      fullPath: '/target-discovery/customers/search'
      preLoaderRoute: typeof AuthenticatedTargetDiscoveryCustomersSearchImport
      parentRoute: typeof AuthenticatedRouteImport
    }
    '/_authenticated/target-discovery/merchants/search': {
      id: '/_authenticated/target-discovery/merchants/search'
      path: '/target-discovery/merchants/search'
      fullPath: '/target-discovery/merchants/search'
      preLoaderRoute: typeof AuthenticatedTargetDiscoveryMerchantsSearchImport
      parentRoute: typeof AuthenticatedRouteImport
    }
    '/_authenticated/merchants/$merchantId/': {
      id: '/_authenticated/merchants/$merchantId/'
      path: '/'
      fullPath: '/merchants/$merchantId/'
      preLoaderRoute: typeof AuthenticatedMerchantsMerchantIdIndexImport
      parentRoute: typeof AuthenticatedMerchantsMerchantIdImport
    }
    '/_authenticated/target-discovery/customers/': {
      id: '/_authenticated/target-discovery/customers/'
      path: '/target-discovery/customers'
      fullPath: '/target-discovery/customers'
      preLoaderRoute: typeof AuthenticatedTargetDiscoveryCustomersIndexImport
      parentRoute: typeof AuthenticatedRouteImport
    }
    '/_authenticated/target-discovery/merchants/': {
      id: '/_authenticated/target-discovery/merchants/'
      path: '/target-discovery/merchants'
      fullPath: '/target-discovery/merchants'
      preLoaderRoute: typeof AuthenticatedTargetDiscoveryMerchantsIndexImport
      parentRoute: typeof AuthenticatedRouteImport
    }
    '/_authenticated/merchants/$merchantId/$branchId/$terminalId': {
      id: '/_authenticated/merchants/$merchantId/$branchId/$terminalId'
      path: '/$terminalId'
      fullPath: '/merchants/$merchantId/$branchId/$terminalId'
      preLoaderRoute: typeof AuthenticatedMerchantsMerchantIdBranchIdTerminalIdImport
      parentRoute: typeof AuthenticatedMerchantsMerchantIdBranchIdImport
    }
    '/_authenticated/merchants/$merchantId/$branchId/': {
      id: '/_authenticated/merchants/$merchantId/$branchId/'
      path: '/'
      fullPath: '/merchants/$merchantId/$branchId/'
      preLoaderRoute: typeof AuthenticatedMerchantsMerchantIdBranchIdIndexImport
      parentRoute: typeof AuthenticatedMerchantsMerchantIdBranchIdImport
    }
  }
}

// Create and export the route tree

interface AuthenticatedAgentsRouteRouteChildren {
  AuthenticatedAgentsAgentIdRouteRoute: typeof AuthenticatedAgentsAgentIdRouteRoute
}

const AuthenticatedAgentsRouteRouteChildren: AuthenticatedAgentsRouteRouteChildren =
  {
    AuthenticatedAgentsAgentIdRouteRoute: AuthenticatedAgentsAgentIdRouteRoute,
  }

const AuthenticatedAgentsRouteRouteWithChildren =
  AuthenticatedAgentsRouteRoute._addFileChildren(
    AuthenticatedAgentsRouteRouteChildren,
  )

interface AuthenticatedBranchAdminsBranchIdRouteChildren {
  AuthenticatedBranchAdminsBranchIdTerminalsRoute: typeof AuthenticatedBranchAdminsBranchIdTerminalsRoute
}

const AuthenticatedBranchAdminsBranchIdRouteChildren: AuthenticatedBranchAdminsBranchIdRouteChildren =
  {
    AuthenticatedBranchAdminsBranchIdTerminalsRoute:
      AuthenticatedBranchAdminsBranchIdTerminalsRoute,
  }

const AuthenticatedBranchAdminsBranchIdRouteWithChildren =
  AuthenticatedBranchAdminsBranchIdRoute._addFileChildren(
    AuthenticatedBranchAdminsBranchIdRouteChildren,
  )

interface AuthenticatedBranchAdminsRouteRouteChildren {
  AuthenticatedBranchAdminsBranchIdRoute: typeof AuthenticatedBranchAdminsBranchIdRouteWithChildren
}

const AuthenticatedBranchAdminsRouteRouteChildren: AuthenticatedBranchAdminsRouteRouteChildren =
  {
    AuthenticatedBranchAdminsBranchIdRoute:
      AuthenticatedBranchAdminsBranchIdRouteWithChildren,
  }

const AuthenticatedBranchAdminsRouteRouteWithChildren =
  AuthenticatedBranchAdminsRouteRoute._addFileChildren(
    AuthenticatedBranchAdminsRouteRouteChildren,
  )

interface AuthenticatedCustomersRouteRouteChildren {
  AuthenticatedCustomersIndexRoute: typeof AuthenticatedCustomersIndexRoute
}

const AuthenticatedCustomersRouteRouteChildren: AuthenticatedCustomersRouteRouteChildren =
  {
    AuthenticatedCustomersIndexRoute: AuthenticatedCustomersIndexRoute,
  }

const AuthenticatedCustomersRouteRouteWithChildren =
  AuthenticatedCustomersRouteRoute._addFileChildren(
    AuthenticatedCustomersRouteRouteChildren,
  )

interface AuthenticatedMerchantsMerchantIdBranchIdRouteChildren {
  AuthenticatedMerchantsMerchantIdBranchIdTerminalIdRoute: typeof AuthenticatedMerchantsMerchantIdBranchIdTerminalIdRoute
  AuthenticatedMerchantsMerchantIdBranchIdIndexRoute: typeof AuthenticatedMerchantsMerchantIdBranchIdIndexRoute
}

const AuthenticatedMerchantsMerchantIdBranchIdRouteChildren: AuthenticatedMerchantsMerchantIdBranchIdRouteChildren =
  {
    AuthenticatedMerchantsMerchantIdBranchIdTerminalIdRoute:
      AuthenticatedMerchantsMerchantIdBranchIdTerminalIdRoute,
    AuthenticatedMerchantsMerchantIdBranchIdIndexRoute:
      AuthenticatedMerchantsMerchantIdBranchIdIndexRoute,
  }

const AuthenticatedMerchantsMerchantIdBranchIdRouteWithChildren =
  AuthenticatedMerchantsMerchantIdBranchIdRoute._addFileChildren(
    AuthenticatedMerchantsMerchantIdBranchIdRouteChildren,
  )

interface AuthenticatedMerchantsMerchantIdRouteChildren {
  AuthenticatedMerchantsMerchantIdBranchIdRoute: typeof AuthenticatedMerchantsMerchantIdBranchIdRouteWithChildren
  AuthenticatedMerchantsMerchantIdBranchesRoute: typeof AuthenticatedMerchantsMerchantIdBranchesRoute
  AuthenticatedMerchantsMerchantIdIndexRoute: typeof AuthenticatedMerchantsMerchantIdIndexRoute
}

const AuthenticatedMerchantsMerchantIdRouteChildren: AuthenticatedMerchantsMerchantIdRouteChildren =
  {
    AuthenticatedMerchantsMerchantIdBranchIdRoute:
      AuthenticatedMerchantsMerchantIdBranchIdRouteWithChildren,
    AuthenticatedMerchantsMerchantIdBranchesRoute:
      AuthenticatedMerchantsMerchantIdBranchesRoute,
    AuthenticatedMerchantsMerchantIdIndexRoute:
      AuthenticatedMerchantsMerchantIdIndexRoute,
  }

const AuthenticatedMerchantsMerchantIdRouteWithChildren =
  AuthenticatedMerchantsMerchantIdRoute._addFileChildren(
    AuthenticatedMerchantsMerchantIdRouteChildren,
  )

interface AuthenticatedMerchantsRouteRouteChildren {
  AuthenticatedMerchantsMerchantIdRoute: typeof AuthenticatedMerchantsMerchantIdRouteWithChildren
  AuthenticatedMerchantsIndexRoute: typeof AuthenticatedMerchantsIndexRoute
}

const AuthenticatedMerchantsRouteRouteChildren: AuthenticatedMerchantsRouteRouteChildren =
  {
    AuthenticatedMerchantsMerchantIdRoute:
      AuthenticatedMerchantsMerchantIdRouteWithChildren,
    AuthenticatedMerchantsIndexRoute: AuthenticatedMerchantsIndexRoute,
  }

const AuthenticatedMerchantsRouteRouteWithChildren =
  AuthenticatedMerchantsRouteRoute._addFileChildren(
    AuthenticatedMerchantsRouteRouteChildren,
  )

interface AuthenticatedSettingsRouteRouteChildren {
  AuthenticatedSettingsAccountRoute: typeof AuthenticatedSettingsAccountRoute
  AuthenticatedSettingsAppearanceRoute: typeof AuthenticatedSettingsAppearanceRoute
  AuthenticatedSettingsDisplayRoute: typeof AuthenticatedSettingsDisplayRoute
  AuthenticatedSettingsNotificationsRoute: typeof AuthenticatedSettingsNotificationsRoute
  AuthenticatedSettingsIndexRoute: typeof AuthenticatedSettingsIndexRoute
}

const AuthenticatedSettingsRouteRouteChildren: AuthenticatedSettingsRouteRouteChildren =
  {
    AuthenticatedSettingsAccountRoute: AuthenticatedSettingsAccountRoute,
    AuthenticatedSettingsAppearanceRoute: AuthenticatedSettingsAppearanceRoute,
    AuthenticatedSettingsDisplayRoute: AuthenticatedSettingsDisplayRoute,
    AuthenticatedSettingsNotificationsRoute:
      AuthenticatedSettingsNotificationsRoute,
    AuthenticatedSettingsIndexRoute: AuthenticatedSettingsIndexRoute,
  }

const AuthenticatedSettingsRouteRouteWithChildren =
  AuthenticatedSettingsRouteRoute._addFileChildren(
    AuthenticatedSettingsRouteRouteChildren,
  )

interface AuthenticatedRouteRouteChildren {
  AuthenticatedAgentsRouteRoute: typeof AuthenticatedAgentsRouteRouteWithChildren
  AuthenticatedBranchAdminsRouteRoute: typeof AuthenticatedBranchAdminsRouteRouteWithChildren
  AuthenticatedCustomersRouteRoute: typeof AuthenticatedCustomersRouteRouteWithChildren
  AuthenticatedMerchantsRouteRoute: typeof AuthenticatedMerchantsRouteRouteWithChildren
  AuthenticatedSettingsRouteRoute: typeof AuthenticatedSettingsRouteRouteWithChildren
  AuthenticatedIndexRoute: typeof AuthenticatedIndexRoute
  AuthenticatedHelpCenterIndexRoute: typeof AuthenticatedHelpCenterIndexRoute
  AuthenticatedTargetDiscoveryIndexRoute: typeof AuthenticatedTargetDiscoveryIndexRoute
  AuthenticatedTargetDiscoveryCustomersSearchRoute: typeof AuthenticatedTargetDiscoveryCustomersSearchRoute
  AuthenticatedTargetDiscoveryMerchantsSearchRoute: typeof AuthenticatedTargetDiscoveryMerchantsSearchRoute
  AuthenticatedTargetDiscoveryCustomersIndexRoute: typeof AuthenticatedTargetDiscoveryCustomersIndexRoute
  AuthenticatedTargetDiscoveryMerchantsIndexRoute: typeof AuthenticatedTargetDiscoveryMerchantsIndexRoute
}

const AuthenticatedRouteRouteChildren: AuthenticatedRouteRouteChildren = {
  AuthenticatedAgentsRouteRoute: AuthenticatedAgentsRouteRouteWithChildren,
  AuthenticatedBranchAdminsRouteRoute:
    AuthenticatedBranchAdminsRouteRouteWithChildren,
  AuthenticatedCustomersRouteRoute:
    AuthenticatedCustomersRouteRouteWithChildren,
  AuthenticatedMerchantsRouteRoute:
    AuthenticatedMerchantsRouteRouteWithChildren,
  AuthenticatedSettingsRouteRoute: AuthenticatedSettingsRouteRouteWithChildren,
  AuthenticatedIndexRoute: AuthenticatedIndexRoute,
  AuthenticatedHelpCenterIndexRoute: AuthenticatedHelpCenterIndexRoute,
  AuthenticatedTargetDiscoveryIndexRoute:
    AuthenticatedTargetDiscoveryIndexRoute,
  AuthenticatedTargetDiscoveryCustomersSearchRoute:
    AuthenticatedTargetDiscoveryCustomersSearchRoute,
  AuthenticatedTargetDiscoveryMerchantsSearchRoute:
    AuthenticatedTargetDiscoveryMerchantsSearchRoute,
  AuthenticatedTargetDiscoveryCustomersIndexRoute:
    AuthenticatedTargetDiscoveryCustomersIndexRoute,
  AuthenticatedTargetDiscoveryMerchantsIndexRoute:
    AuthenticatedTargetDiscoveryMerchantsIndexRoute,
}

const AuthenticatedRouteRouteWithChildren =
  AuthenticatedRouteRoute._addFileChildren(AuthenticatedRouteRouteChildren)

interface ClerkauthRouteRouteChildren {
  ClerkauthSignInRoute: typeof ClerkauthSignInRoute
  ClerkauthSignUpRoute: typeof ClerkauthSignUpRoute
}

const ClerkauthRouteRouteChildren: ClerkauthRouteRouteChildren = {
  ClerkauthSignInRoute: ClerkauthSignInRoute,
  ClerkauthSignUpRoute: ClerkauthSignUpRoute,
}

const ClerkauthRouteRouteWithChildren = ClerkauthRouteRoute._addFileChildren(
  ClerkauthRouteRouteChildren,
)

interface ClerkAuthenticatedRouteRouteChildren {
  ClerkAuthenticatedUserManagementRoute: typeof ClerkAuthenticatedUserManagementRoute
}

const ClerkAuthenticatedRouteRouteChildren: ClerkAuthenticatedRouteRouteChildren =
  {
    ClerkAuthenticatedUserManagementRoute:
      ClerkAuthenticatedUserManagementRoute,
  }

const ClerkAuthenticatedRouteRouteWithChildren =
  ClerkAuthenticatedRouteRoute._addFileChildren(
    ClerkAuthenticatedRouteRouteChildren,
  )

interface ClerkRouteRouteChildren {
  ClerkauthRouteRoute: typeof ClerkauthRouteRouteWithChildren
  ClerkAuthenticatedRouteRoute: typeof ClerkAuthenticatedRouteRouteWithChildren
}

const ClerkRouteRouteChildren: ClerkRouteRouteChildren = {
  ClerkauthRouteRoute: ClerkauthRouteRouteWithChildren,
  ClerkAuthenticatedRouteRoute: ClerkAuthenticatedRouteRouteWithChildren,
}

const ClerkRouteRouteWithChildren = ClerkRouteRoute._addFileChildren(
  ClerkRouteRouteChildren,
)

export interface FileRoutesByFullPath {
  '': typeof AuthenticatedRouteRouteWithChildren
  '/clerk': typeof ClerkAuthenticatedRouteRouteWithChildren
  '/agents': typeof AuthenticatedAgentsRouteRouteWithChildren
  '/branch-admins': typeof AuthenticatedBranchAdminsRouteRouteWithChildren
  '/customers': typeof AuthenticatedCustomersRouteRouteWithChildren
  '/merchants': typeof AuthenticatedMerchantsRouteRouteWithChildren
  '/settings': typeof AuthenticatedSettingsRouteRouteWithChildren
  '/clerk/': typeof ClerkauthRouteRouteWithChildren
  '/forgot-password': typeof authForgotPasswordRoute
  '/otp': typeof authOtpRoute
  '/sign-in': typeof authSignInRoute
  '/sign-in-2': typeof authSignIn2Route
  '/sign-up': typeof authSignUpRoute
  '/401': typeof errors401Route
  '/403': typeof errors403Route
  '/404': typeof errors404Route
  '/500': typeof errors500Route
  '/503': typeof errors503Route
  '/': typeof AuthenticatedIndexRoute
  '/agents/$agentId': typeof AuthenticatedAgentsAgentIdRouteRoute
  '/branch-admins/$branchId': typeof AuthenticatedBranchAdminsBranchIdRouteWithChildren
  '/merchants/$merchantId': typeof AuthenticatedMerchantsMerchantIdRouteWithChildren
  '/settings/account': typeof AuthenticatedSettingsAccountRoute
  '/settings/appearance': typeof AuthenticatedSettingsAppearanceRoute
  '/settings/display': typeof AuthenticatedSettingsDisplayRoute
  '/settings/notifications': typeof AuthenticatedSettingsNotificationsRoute
  '/clerk/sign-in': typeof ClerkauthSignInRoute
  '/clerk/sign-up': typeof ClerkauthSignUpRoute
  '/clerk/user-management': typeof ClerkAuthenticatedUserManagementRoute
  '/customers/': typeof AuthenticatedCustomersIndexRoute
  '/help-center': typeof AuthenticatedHelpCenterIndexRoute
  '/merchants/': typeof AuthenticatedMerchantsIndexRoute
  '/settings/': typeof AuthenticatedSettingsIndexRoute
  '/target-discovery': typeof AuthenticatedTargetDiscoveryIndexRoute
  '/branch-admins/$branchId/terminals': typeof AuthenticatedBranchAdminsBranchIdTerminalsRoute
  '/merchants/$merchantId/$branchId': typeof AuthenticatedMerchantsMerchantIdBranchIdRouteWithChildren
  '/merchants/$merchantId/branches': typeof AuthenticatedMerchantsMerchantIdBranchesRoute
  '/target-discovery/customers/search': typeof AuthenticatedTargetDiscoveryCustomersSearchRoute
  '/target-discovery/merchants/search': typeof AuthenticatedTargetDiscoveryMerchantsSearchRoute
  '/merchants/$merchantId/': typeof AuthenticatedMerchantsMerchantIdIndexRoute
  '/target-discovery/customers': typeof AuthenticatedTargetDiscoveryCustomersIndexRoute
  '/target-discovery/merchants': typeof AuthenticatedTargetDiscoveryMerchantsIndexRoute
  '/merchants/$merchantId/$branchId/$terminalId': typeof AuthenticatedMerchantsMerchantIdBranchIdTerminalIdRoute
  '/merchants/$merchantId/$branchId/': typeof AuthenticatedMerchantsMerchantIdBranchIdIndexRoute
}

export interface FileRoutesByTo {
  '/agents': typeof AuthenticatedAgentsRouteRouteWithChildren
  '/branch-admins': typeof AuthenticatedBranchAdminsRouteRouteWithChildren
  '/clerk': typeof ClerkAuthenticatedRouteRouteWithChildren
  '/forgot-password': typeof authForgotPasswordRoute
  '/otp': typeof authOtpRoute
  '/sign-in': typeof authSignInRoute
  '/sign-in-2': typeof authSignIn2Route
  '/sign-up': typeof authSignUpRoute
  '/401': typeof errors401Route
  '/403': typeof errors403Route
  '/404': typeof errors404Route
  '/500': typeof errors500Route
  '/503': typeof errors503Route
  '/': typeof AuthenticatedIndexRoute
  '/agents/$agentId': typeof AuthenticatedAgentsAgentIdRouteRoute
  '/branch-admins/$branchId': typeof AuthenticatedBranchAdminsBranchIdRouteWithChildren
  '/settings/account': typeof AuthenticatedSettingsAccountRoute
  '/settings/appearance': typeof AuthenticatedSettingsAppearanceRoute
  '/settings/display': typeof AuthenticatedSettingsDisplayRoute
  '/settings/notifications': typeof AuthenticatedSettingsNotificationsRoute
  '/clerk/sign-in': typeof ClerkauthSignInRoute
  '/clerk/sign-up': typeof ClerkauthSignUpRoute
  '/clerk/user-management': typeof ClerkAuthenticatedUserManagementRoute
  '/customers': typeof AuthenticatedCustomersIndexRoute
  '/help-center': typeof AuthenticatedHelpCenterIndexRoute
  '/merchants': typeof AuthenticatedMerchantsIndexRoute
  '/settings': typeof AuthenticatedSettingsIndexRoute
  '/target-discovery': typeof AuthenticatedTargetDiscoveryIndexRoute
  '/branch-admins/$branchId/terminals': typeof AuthenticatedBranchAdminsBranchIdTerminalsRoute
  '/merchants/$merchantId/branches': typeof AuthenticatedMerchantsMerchantIdBranchesRoute
  '/target-discovery/customers/search': typeof AuthenticatedTargetDiscoveryCustomersSearchRoute
  '/target-discovery/merchants/search': typeof AuthenticatedTargetDiscoveryMerchantsSearchRoute
  '/merchants/$merchantId': typeof AuthenticatedMerchantsMerchantIdIndexRoute
  '/target-discovery/customers': typeof AuthenticatedTargetDiscoveryCustomersIndexRoute
  '/target-discovery/merchants': typeof AuthenticatedTargetDiscoveryMerchantsIndexRoute
  '/merchants/$merchantId/$branchId/$terminalId': typeof AuthenticatedMerchantsMerchantIdBranchIdTerminalIdRoute
  '/merchants/$merchantId/$branchId': typeof AuthenticatedMerchantsMerchantIdBranchIdIndexRoute
}

export interface FileRoutesById {
  __root__: typeof rootRoute
  '/_authenticated': typeof AuthenticatedRouteRouteWithChildren
  '/clerk': typeof ClerkRouteRouteWithChildren
  '/_authenticated/agents': typeof AuthenticatedAgentsRouteRouteWithChildren
  '/_authenticated/branch-admins': typeof AuthenticatedBranchAdminsRouteRouteWithChildren
  '/_authenticated/customers': typeof AuthenticatedCustomersRouteRouteWithChildren
  '/_authenticated/merchants': typeof AuthenticatedMerchantsRouteRouteWithChildren
  '/_authenticated/settings': typeof AuthenticatedSettingsRouteRouteWithChildren
  '/clerk/(auth)': typeof ClerkauthRouteRouteWithChildren
  '/clerk/_authenticated': typeof ClerkAuthenticatedRouteRouteWithChildren
  '/(auth)/forgot-password': typeof authForgotPasswordRoute
  '/(auth)/otp': typeof authOtpRoute
  '/(auth)/sign-in': typeof authSignInRoute
  '/(auth)/sign-in-2': typeof authSignIn2Route
  '/(auth)/sign-up': typeof authSignUpRoute
  '/(errors)/401': typeof errors401Route
  '/(errors)/403': typeof errors403Route
  '/(errors)/404': typeof errors404Route
  '/(errors)/500': typeof errors500Route
  '/(errors)/503': typeof errors503Route
  '/_authenticated/': typeof AuthenticatedIndexRoute
  '/_authenticated/agents/$agentId': typeof AuthenticatedAgentsAgentIdRouteRoute
  '/_authenticated/branch-admins/$branchId': typeof AuthenticatedBranchAdminsBranchIdRouteWithChildren
  '/_authenticated/merchants/$merchantId': typeof AuthenticatedMerchantsMerchantIdRouteWithChildren
  '/_authenticated/settings/account': typeof AuthenticatedSettingsAccountRoute
  '/_authenticated/settings/appearance': typeof AuthenticatedSettingsAppearanceRoute
  '/_authenticated/settings/display': typeof AuthenticatedSettingsDisplayRoute
  '/_authenticated/settings/notifications': typeof AuthenticatedSettingsNotificationsRoute
  '/clerk/(auth)/sign-in': typeof ClerkauthSignInRoute
  '/clerk/(auth)/sign-up': typeof ClerkauthSignUpRoute
  '/clerk/_authenticated/user-management': typeof ClerkAuthenticatedUserManagementRoute
  '/_authenticated/customers/': typeof AuthenticatedCustomersIndexRoute
  '/_authenticated/help-center/': typeof AuthenticatedHelpCenterIndexRoute
  '/_authenticated/merchants/': typeof AuthenticatedMerchantsIndexRoute
  '/_authenticated/settings/': typeof AuthenticatedSettingsIndexRoute
  '/_authenticated/target-discovery/': typeof AuthenticatedTargetDiscoveryIndexRoute
  '/_authenticated/branch-admins/$branchId/terminals': typeof AuthenticatedBranchAdminsBranchIdTerminalsRoute
  '/_authenticated/merchants/$merchantId/$branchId': typeof AuthenticatedMerchantsMerchantIdBranchIdRouteWithChildren
  '/_authenticated/merchants/$merchantId/branches': typeof AuthenticatedMerchantsMerchantIdBranchesRoute
  '/_authenticated/target-discovery/customers/search': typeof AuthenticatedTargetDiscoveryCustomersSearchRoute
  '/_authenticated/target-discovery/merchants/search': typeof AuthenticatedTargetDiscoveryMerchantsSearchRoute
  '/_authenticated/merchants/$merchantId/': typeof AuthenticatedMerchantsMerchantIdIndexRoute
  '/_authenticated/target-discovery/customers/': typeof AuthenticatedTargetDiscoveryCustomersIndexRoute
  '/_authenticated/target-discovery/merchants/': typeof AuthenticatedTargetDiscoveryMerchantsIndexRoute
  '/_authenticated/merchants/$merchantId/$branchId/$terminalId': typeof AuthenticatedMerchantsMerchantIdBranchIdTerminalIdRoute
  '/_authenticated/merchants/$merchantId/$branchId/': typeof AuthenticatedMerchantsMerchantIdBranchIdIndexRoute
}

export interface FileRouteTypes {
  fileRoutesByFullPath: FileRoutesByFullPath
  fullPaths:
    | ''
    | '/clerk'
    | '/agents'
    | '/branch-admins'
    | '/customers'
    | '/merchants'
    | '/settings'
    | '/clerk/'
    | '/forgot-password'
    | '/otp'
    | '/sign-in'
    | '/sign-in-2'
    | '/sign-up'
    | '/401'
    | '/403'
    | '/404'
    | '/500'
    | '/503'
    | '/'
    | '/agents/$agentId'
    | '/branch-admins/$branchId'
    | '/merchants/$merchantId'
    | '/settings/account'
    | '/settings/appearance'
    | '/settings/display'
    | '/settings/notifications'
    | '/clerk/sign-in'
    | '/clerk/sign-up'
    | '/clerk/user-management'
    | '/customers/'
    | '/help-center'
    | '/merchants/'
    | '/settings/'
    | '/target-discovery'
    | '/branch-admins/$branchId/terminals'
    | '/merchants/$merchantId/$branchId'
    | '/merchants/$merchantId/branches'
    | '/target-discovery/customers/search'
    | '/target-discovery/merchants/search'
    | '/merchants/$merchantId/'
    | '/target-discovery/customers'
    | '/target-discovery/merchants'
    | '/merchants/$merchantId/$branchId/$terminalId'
    | '/merchants/$merchantId/$branchId/'
  fileRoutesByTo: FileRoutesByTo
  to:
    | '/agents'
    | '/branch-admins'
    | '/clerk'
    | '/forgot-password'
    | '/otp'
    | '/sign-in'
    | '/sign-in-2'
    | '/sign-up'
    | '/401'
    | '/403'
    | '/404'
    | '/500'
    | '/503'
    | '/'
    | '/agents/$agentId'
    | '/branch-admins/$branchId'
    | '/settings/account'
    | '/settings/appearance'
    | '/settings/display'
    | '/settings/notifications'
    | '/clerk/sign-in'
    | '/clerk/sign-up'
    | '/clerk/user-management'
    | '/customers'
    | '/help-center'
    | '/merchants'
    | '/settings'
    | '/target-discovery'
    | '/branch-admins/$branchId/terminals'
    | '/merchants/$merchantId/branches'
    | '/target-discovery/customers/search'
    | '/target-discovery/merchants/search'
    | '/merchants/$merchantId'
    | '/target-discovery/customers'
    | '/target-discovery/merchants'
    | '/merchants/$merchantId/$branchId/$terminalId'
    | '/merchants/$merchantId/$branchId'
  id:
    | '__root__'
    | '/_authenticated'
    | '/clerk'
    | '/_authenticated/agents'
    | '/_authenticated/branch-admins'
    | '/_authenticated/customers'
    | '/_authenticated/merchants'
    | '/_authenticated/settings'
    | '/clerk/(auth)'
    | '/clerk/_authenticated'
    | '/(auth)/forgot-password'
    | '/(auth)/otp'
    | '/(auth)/sign-in'
    | '/(auth)/sign-in-2'
    | '/(auth)/sign-up'
    | '/(errors)/401'
    | '/(errors)/403'
    | '/(errors)/404'
    | '/(errors)/500'
    | '/(errors)/503'
    | '/_authenticated/'
    | '/_authenticated/agents/$agentId'
    | '/_authenticated/branch-admins/$branchId'
    | '/_authenticated/merchants/$merchantId'
    | '/_authenticated/settings/account'
    | '/_authenticated/settings/appearance'
    | '/_authenticated/settings/display'
    | '/_authenticated/settings/notifications'
    | '/clerk/(auth)/sign-in'
    | '/clerk/(auth)/sign-up'
    | '/clerk/_authenticated/user-management'
    | '/_authenticated/customers/'
    | '/_authenticated/help-center/'
    | '/_authenticated/merchants/'
    | '/_authenticated/settings/'
    | '/_authenticated/target-discovery/'
    | '/_authenticated/branch-admins/$branchId/terminals'
    | '/_authenticated/merchants/$merchantId/$branchId'
    | '/_authenticated/merchants/$merchantId/branches'
    | '/_authenticated/target-discovery/customers/search'
    | '/_authenticated/target-discovery/merchants/search'
    | '/_authenticated/merchants/$merchantId/'
    | '/_authenticated/target-discovery/customers/'
    | '/_authenticated/target-discovery/merchants/'
    | '/_authenticated/merchants/$merchantId/$branchId/$terminalId'
    | '/_authenticated/merchants/$merchantId/$branchId/'
  fileRoutesById: FileRoutesById
}

export interface RootRouteChildren {
  AuthenticatedRouteRoute: typeof AuthenticatedRouteRouteWithChildren
  ClerkRouteRoute: typeof ClerkRouteRouteWithChildren
  authForgotPasswordRoute: typeof authForgotPasswordRoute
  authOtpRoute: typeof authOtpRoute
  authSignInRoute: typeof authSignInRoute
  authSignIn2Route: typeof authSignIn2Route
  authSignUpRoute: typeof authSignUpRoute
  errors401Route: typeof errors401Route
  errors403Route: typeof errors403Route
  errors404Route: typeof errors404Route
  errors500Route: typeof errors500Route
  errors503Route: typeof errors503Route
}

const rootRouteChildren: RootRouteChildren = {
  AuthenticatedRouteRoute: AuthenticatedRouteRouteWithChildren,
  ClerkRouteRoute: ClerkRouteRouteWithChildren,
  authForgotPasswordRoute: authForgotPasswordRoute,
  authOtpRoute: authOtpRoute,
  authSignInRoute: authSignInRoute,
  authSignIn2Route: authSignIn2Route,
  authSignUpRoute: authSignUpRoute,
  errors401Route: errors401Route,
  errors403Route: errors403Route,
  errors404Route: errors404Route,
  errors500Route: errors500Route,
  errors503Route: errors503Route,
}

export const routeTree = rootRoute
  ._addFileChildren(rootRouteChildren)
  ._addFileTypes<FileRouteTypes>()

/* ROUTE_MANIFEST_START
{
  "routes": {
    "__root__": {
      "filePath": "__root.tsx",
      "children": [
        "/_authenticated",
        "/clerk",
        "/(auth)/forgot-password",
        "/(auth)/otp",
        "/(auth)/sign-in",
        "/(auth)/sign-in-2",
        "/(auth)/sign-up",
        "/(errors)/401",
        "/(errors)/403",
        "/(errors)/404",
        "/(errors)/500",
        "/(errors)/503"
      ]
    },
    "/_authenticated": {
      "filePath": "_authenticated/route.tsx",
      "children": [
        "/_authenticated/agents",
        "/_authenticated/branch-admins",
        "/_authenticated/customers",
        "/_authenticated/merchants",
        "/_authenticated/settings",
        "/_authenticated/",
        "/_authenticated/help-center/",
        "/_authenticated/target-discovery/",
        "/_authenticated/target-discovery/customers/search",
        "/_authenticated/target-discovery/merchants/search",
        "/_authenticated/target-discovery/customers/",
        "/_authenticated/target-discovery/merchants/"
      ]
    },
    "/clerk": {
      "filePath": "clerk/route.tsx",
      "children": [
        "/clerk/(auth)",
        "/clerk/_authenticated"
      ]
    },
    "/_authenticated/agents": {
      "filePath": "_authenticated/agents/route.tsx",
      "parent": "/_authenticated",
      "children": [
        "/_authenticated/agents/$agentId"
      ]
    },
    "/_authenticated/branch-admins": {
      "filePath": "_authenticated/branch-admins/route.tsx",
      "parent": "/_authenticated",
      "children": [
        "/_authenticated/branch-admins/$branchId"
      ]
    },
    "/_authenticated/customers": {
      "filePath": "_authenticated/customers/route.tsx",
      "parent": "/_authenticated",
      "children": [
        "/_authenticated/customers/"
      ]
    },
    "/_authenticated/merchants": {
      "filePath": "_authenticated/merchants/route.tsx",
      "parent": "/_authenticated",
      "children": [
        "/_authenticated/merchants/$merchantId",
        "/_authenticated/merchants/"
      ]
    },
    "/_authenticated/settings": {
      "filePath": "_authenticated/settings/route.tsx",
      "parent": "/_authenticated",
      "children": [
        "/_authenticated/settings/account",
        "/_authenticated/settings/appearance",
        "/_authenticated/settings/display",
        "/_authenticated/settings/notifications",
        "/_authenticated/settings/"
      ]
    },
    "/clerk/(auth)": {
      "filePath": "clerk/(auth)/route.tsx",
      "parent": "/clerk",
      "children": [
        "/clerk/(auth)/sign-in",
        "/clerk/(auth)/sign-up"
      ]
    },
    "/clerk/_authenticated": {
      "filePath": "clerk/_authenticated/route.tsx",
      "parent": "/clerk",
      "children": [
        "/clerk/_authenticated/user-management"
      ]
    },
    "/(auth)/forgot-password": {
      "filePath": "(auth)/forgot-password.tsx"
    },
    "/(auth)/otp": {
      "filePath": "(auth)/otp.tsx"
    },
    "/(auth)/sign-in": {
      "filePath": "(auth)/sign-in.tsx"
    },
    "/(auth)/sign-in-2": {
      "filePath": "(auth)/sign-in-2.tsx"
    },
    "/(auth)/sign-up": {
      "filePath": "(auth)/sign-up.tsx"
    },
    "/(errors)/401": {
      "filePath": "(errors)/401.tsx"
    },
    "/(errors)/403": {
      "filePath": "(errors)/403.tsx"
    },
    "/(errors)/404": {
      "filePath": "(errors)/404.tsx"
    },
    "/(errors)/500": {
      "filePath": "(errors)/500.tsx"
    },
    "/(errors)/503": {
      "filePath": "(errors)/503.tsx"
    },
    "/_authenticated/": {
      "filePath": "_authenticated/index.tsx",
      "parent": "/_authenticated"
    },
    "/_authenticated/agents/$agentId": {
      "filePath": "_authenticated/agents/$agentId/route.tsx",
      "parent": "/_authenticated/agents"
    },
    "/_authenticated/branch-admins/$branchId": {
      "filePath": "_authenticated/branch-admins/$branchId.tsx",
      "parent": "/_authenticated/branch-admins",
      "children": [
        "/_authenticated/branch-admins/$branchId/terminals"
      ]
    },
    "/_authenticated/merchants/$merchantId": {
      "filePath": "_authenticated/merchants/$merchantId.tsx",
      "parent": "/_authenticated/merchants",
      "children": [
        "/_authenticated/merchants/$merchantId/$branchId",
        "/_authenticated/merchants/$merchantId/branches",
        "/_authenticated/merchants/$merchantId/"
      ]
    },
    "/_authenticated/settings/account": {
      "filePath": "_authenticated/settings/account.tsx",
      "parent": "/_authenticated/settings"
    },
    "/_authenticated/settings/appearance": {
      "filePath": "_authenticated/settings/appearance.tsx",
      "parent": "/_authenticated/settings"
    },
    "/_authenticated/settings/display": {
      "filePath": "_authenticated/settings/display.tsx",
      "parent": "/_authenticated/settings"
    },
    "/_authenticated/settings/notifications": {
      "filePath": "_authenticated/settings/notifications.tsx",
      "parent": "/_authenticated/settings"
    },
    "/clerk/(auth)/sign-in": {
      "filePath": "clerk/(auth)/sign-in.tsx",
      "parent": "/clerk/(auth)"
    },
    "/clerk/(auth)/sign-up": {
      "filePath": "clerk/(auth)/sign-up.tsx",
      "parent": "/clerk/(auth)"
    },
    "/clerk/_authenticated/user-management": {
      "filePath": "clerk/_authenticated/user-management.tsx",
      "parent": "/clerk/_authenticated"
    },
    "/_authenticated/customers/": {
      "filePath": "_authenticated/customers/index.tsx",
      "parent": "/_authenticated/customers"
    },
    "/_authenticated/help-center/": {
      "filePath": "_authenticated/help-center/index.tsx",
      "parent": "/_authenticated"
    },
    "/_authenticated/merchants/": {
      "filePath": "_authenticated/merchants/index.tsx",
      "parent": "/_authenticated/merchants"
    },
    "/_authenticated/settings/": {
      "filePath": "_authenticated/settings/index.tsx",
      "parent": "/_authenticated/settings"
    },
    "/_authenticated/target-discovery/": {
      "filePath": "_authenticated/target-discovery/index.tsx",
      "parent": "/_authenticated"
    },
    "/_authenticated/branch-admins/$branchId/terminals": {
      "filePath": "_authenticated/branch-admins/$branchId/terminals.tsx",
      "parent": "/_authenticated/branch-admins/$branchId"
    },
    "/_authenticated/merchants/$merchantId/$branchId": {
      "filePath": "_authenticated/merchants/$merchantId/$branchId.tsx",
      "parent": "/_authenticated/merchants/$merchantId",
      "children": [
        "/_authenticated/merchants/$merchantId/$branchId/$terminalId",
        "/_authenticated/merchants/$merchantId/$branchId/"
      ]
    },
    "/_authenticated/merchants/$merchantId/branches": {
      "filePath": "_authenticated/merchants/$merchantId/branches.tsx",
      "parent": "/_authenticated/merchants/$merchantId"
    },
    "/_authenticated/target-discovery/customers/search": {
      "filePath": "_authenticated/target-discovery/customers/search.tsx",
      "parent": "/_authenticated"
    },
    "/_authenticated/target-discovery/merchants/search": {
      "filePath": "_authenticated/target-discovery/merchants/search.tsx",
      "parent": "/_authenticated"
    },
    "/_authenticated/merchants/$merchantId/": {
      "filePath": "_authenticated/merchants/$merchantId/index.tsx",
      "parent": "/_authenticated/merchants/$merchantId"
    },
    "/_authenticated/target-discovery/customers/": {
      "filePath": "_authenticated/target-discovery/customers/index.tsx",
      "parent": "/_authenticated"
    },
    "/_authenticated/target-discovery/merchants/": {
      "filePath": "_authenticated/target-discovery/merchants/index.tsx",
      "parent": "/_authenticated"
    },
    "/_authenticated/merchants/$merchantId/$branchId/$terminalId": {
      "filePath": "_authenticated/merchants/$merchantId/$branchId/$terminalId.tsx",
      "parent": "/_authenticated/merchants/$merchantId/$branchId"
    },
    "/_authenticated/merchants/$merchantId/$branchId/": {
      "filePath": "_authenticated/merchants/$merchantId/$branchId/index.tsx",
      "parent": "/_authenticated/merchants/$merchantId/$branchId"
    }
  }
}
ROUTE_MANIFEST_END */
