import { useState } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs'
import { useTerminalTransactionFrequencyAnalysis } from '@/hooks/use-terminals'
import type { DateFilters } from '@/types/api'
import { BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer } from 'recharts'
import { useTheme } from '@/context/theme-context'

interface TerminalTransactionFrequencyAnalysisProps {
  terminalId: string
  dateFilters: DateFilters
}

export function TerminalTransactionFrequencyAnalysis({
  terminalId,
  dateFilters
}: TerminalTransactionFrequencyAnalysisProps) {
  const { theme } = useTheme()
  const [activeTab, setActiveTab] = useState('summary')

  const { data, isLoading, error } = useTerminalTransactionFrequencyAnalysis(
    terminalId,
    dateFilters,
    !!terminalId
  )

  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Transaction Frequency Analysis</CardTitle>
          <CardDescription>
            Analyzing transaction patterns and frequency for this terminal
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="h-[400px] flex items-center justify-center">
            <div className="text-muted-foreground">Loading frequency analysis...</div>
          </div>
        </CardContent>
      </Card>
    )
  }

  if (error) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Transaction Frequency Analysis</CardTitle>
          <CardDescription>
            Analyzing transaction patterns and frequency for this terminal
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="h-[400px] flex items-center justify-center">
            <div className="text-muted-foreground">Error loading frequency analysis</div>
          </div>
        </CardContent>
      </Card>
    )
  }

  if (!terminalId) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Transaction Frequency Analysis</CardTitle>
          <CardDescription>
            Analyzing transaction patterns and frequency for this terminal
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="h-[400px] flex items-center justify-center">
            <div className="text-muted-foreground">Please select a terminal to view data</div>
          </div>
        </CardContent>
      </Card>
    )
  }

  const analysisData = Array.isArray(data?.data) ? data.data : []

  if (analysisData.length === 0) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Transaction Frequency Analysis</CardTitle>
          <CardDescription>
            Analyzing transaction patterns and frequency for this terminal
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="h-[400px] flex items-center justify-center">
            <div className="text-muted-foreground">No frequency data available</div>
          </div>
        </CardContent>
      </Card>
    )
  }

  // Get chart colors based on theme
  const getChartColors = () => {
    if (theme === 'transflow') {
      return {
        primary: '#08518A',
        secondary: '#3182CE',
        grid: '#E2E8F0',
        text: '#2D3748'
      }
    } else if (theme === 'dark') {
      return {
        primary: '#60A5FA',
        secondary: '#93C5FD',
        grid: '#374151',
        text: '#F9FAFB'
      }
    } else {
      return {
        primary: '#3B82F6',
        secondary: '#60A5FA',
        grid: '#E5E7EB',
        text: '#111827'
      }
    }
  }

  const colors = getChartColors()

  // Group data by analysis type
  const summaryData = analysisData.filter(item => item.analysis_type === 'summary')
  const dailyData = analysisData.filter(item => item.analysis_type === 'daily')
  const hourlyData = analysisData.filter(item => item.analysis_type === 'hourly')
  const monthlyData = analysisData.filter(item => item.analysis_type === 'monthly')
  const quarterlyData = analysisData.filter(item => item.analysis_type === 'quarterly')

  const renderChart = (chartData: any[], dataKey: string, title: string) => (
    <div className="h-[300px]">
      <ResponsiveContainer width="100%" height="100%">
        <BarChart data={chartData} margin={{ top: 20, right: 30, left: 20, bottom: 5 }}>
          <CartesianGrid strokeDasharray="3 3" stroke={colors.grid} />
          <XAxis 
            dataKey="period" 
            stroke={colors.text}
            fontSize={12}
          />
          <YAxis 
            stroke={colors.text}
            fontSize={12}
          />
          <Tooltip 
            contentStyle={{
              backgroundColor: theme === 'dark' ? '#1F2937' : '#FFFFFF',
              border: `1px solid ${colors.grid}`,
              borderRadius: '6px'
            }}
          />
          <Bar 
            dataKey={dataKey} 
            fill={colors.primary}
            radius={[2, 2, 0, 0]}
          />
        </BarChart>
      </ResponsiveContainer>
    </div>
  )

  return (
    <Card>
      <CardHeader>
        <CardTitle>Transaction Frequency Analysis</CardTitle>
        <CardDescription>
          Analyzing transaction patterns and frequency for this terminal
        </CardDescription>
      </CardHeader>
      <CardContent>
        <Tabs value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="grid w-full grid-cols-5">
            <TabsTrigger value="summary">Summary</TabsTrigger>
            <TabsTrigger value="daily">Daily</TabsTrigger>
            <TabsTrigger value="hourly">Hourly</TabsTrigger>
            <TabsTrigger value="monthly">Monthly</TabsTrigger>
            <TabsTrigger value="quarterly">Quarterly</TabsTrigger>
          </TabsList>

          <TabsContent value="summary" className="space-y-4">
            <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
              {summaryData.map((item, index) => (
                <Card key={index}>
                  <CardHeader className="pb-2">
                    <CardTitle className="text-sm font-medium">
                      {item.metric || 'Summary Metric'}
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">
                      {typeof item.value === 'number' ? item.value.toLocaleString() : item.value}
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </TabsContent>

          <TabsContent value="daily">
            {renderChart(dailyData, 'transaction_count', 'Daily Transaction Pattern')}
          </TabsContent>

          <TabsContent value="hourly">
            {renderChart(hourlyData, 'transaction_count', 'Hourly Transaction Pattern')}
          </TabsContent>

          <TabsContent value="monthly">
            {renderChart(monthlyData, 'transaction_count', 'Monthly Transaction Pattern')}
          </TabsContent>

          <TabsContent value="quarterly">
            {renderChart(quarterlyData, 'transaction_count', 'Quarterly Transaction Pattern')}
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  )
}
