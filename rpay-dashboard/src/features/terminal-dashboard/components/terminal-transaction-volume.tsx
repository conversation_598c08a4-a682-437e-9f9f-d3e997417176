import { useTerminalTransactionVolume, useTerminalTransactionCount } from '@/hooks/use-terminals'
import type { DateFilters } from '@/types/api'
import { AreaChart, Area, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer } from 'recharts'
import { useTheme } from '@/components/theme-provider'

interface TerminalTransactionVolumeProps {
  terminalId: string
  granularity: 'daily' | 'weekly' | 'monthly' | 'yearly'
  mode: 'amount' | 'count'
  dateFilters: DateFilters
}

export function TerminalTransactionVolume({
  terminalId,
  granularity,
  mode,
  dateFilters
}: TerminalTransactionVolumeProps) {
  const { theme } = useTheme()

  const { data: volumeData, isLoading: volumeLoading } = useTerminalTransactionVolume(
    terminalId,
    { granularity, ...dateFilters },
    !!terminalId
  )

  const { data: countData, isLoading: countLoading } = useTerminalTransactionCount(
    terminalId,
    { granularity, ...dateFilters },
    !!terminalId
  )

  const isLoading = volumeLoading || countLoading

  if (isLoading) {
    return (
      <div className="h-[300px] flex items-center justify-center">
        <div className="text-muted-foreground">Loading chart data...</div>
      </div>
    )
  }

  // Use the appropriate data based on mode
  const chartData = mode === 'amount' ? volumeData : countData
  
  if (!chartData?.data?.labels || !chartData?.data?.values) {
    return (
      <div className="h-[300px] flex items-center justify-center">
        <div className="text-muted-foreground">No chart data available</div>
      </div>
    )
  }

  // Transform data for recharts
  const transformedData = chartData.data.labels.map((label: string, index: number) => ({
    period: label,
    value: chartData.data.values[index] || 0
  }))

  // Get chart colors based on theme
  const getChartColors = () => {
    if (theme === 'transflow') {
      return {
        primary: '#08518A',
        secondary: '#3182CE',
        grid: '#E2E8F0',
        text: '#2D3748'
      }
    } else if (theme === 'dark') {
      return {
        primary: '#60A5FA',
        secondary: '#93C5FD',
        grid: '#374151',
        text: '#F9FAFB'
      }
    } else {
      return {
        primary: '#3B82F6',
        secondary: '#60A5FA',
        grid: '#E5E7EB',
        text: '#111827'
      }
    }
  }

  const colors = getChartColors()

  return (
    <div className="h-[300px]">
      <ResponsiveContainer width="100%" height="100%">
        <AreaChart data={transformedData} margin={{ top: 20, right: 30, left: 20, bottom: 5 }}>
          <CartesianGrid strokeDasharray="3 3" stroke={colors.grid} />
          <XAxis 
            dataKey="period" 
            stroke={colors.text}
            fontSize={12}
          />
          <YAxis 
            stroke={colors.text}
            fontSize={12}
            tickFormatter={(value) => {
              if (mode === 'amount') {
                return `₵${value.toLocaleString()}`
              }
              return value.toLocaleString()
            }}
          />
          <Tooltip 
            contentStyle={{
              backgroundColor: theme === 'dark' ? '#1F2937' : '#FFFFFF',
              border: `1px solid ${colors.grid}`,
              borderRadius: '6px'
            }}
            formatter={(value: number) => {
              if (mode === 'amount') {
                return [`₵${value.toLocaleString()}`, 'Transaction Value']
              }
              return [value.toLocaleString(), 'Transaction Count']
            }}
          />
          <Area 
            type="monotone" 
            dataKey="value" 
            stroke={colors.primary}
            fill={colors.primary}
            fillOpacity={0.3}
            strokeWidth={2}
          />
        </AreaChart>
      </ResponsiveContainer>
    </div>
  )
}
