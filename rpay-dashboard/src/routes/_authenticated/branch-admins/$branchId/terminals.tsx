import { createFileRoute } from '@tanstack/react-router'
import { useState } from 'react'
import { useQuery } from '@tanstack/react-query'
import { useBranchDetails } from '@/hooks/use-branches'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Badge } from '@/components/ui/badge'
import { ArrowLeft, Search, Monitor, ExternalLink } from 'lucide-react'
import { Avatar, AvatarFallback } from '@/components/ui/avatar'
import { useNavigate } from '@tanstack/react-router'

interface Terminal {
  terminal_id: string
  total_amount: number
  transaction_count: number
}

interface TerminalsResponse {
  data: Terminal[]
  pagination: {
    page: number
    page_size: number
    total_items: number
    total_pages: number
  }
  filters: {
    search: string
  }
  sort: {
    sort_by: string
    sort_order: string
  }
}

function TerminalsList() {
  const { branchId } = Route.useParams()
  const navigate = useNavigate()
  const [page, setPage] = useState(1)
  const [search, setSearch] = useState('')
  const [sortBy, setSortBy] = useState('total_amount')
  const [sortOrder, setSortOrder] = useState('desc')

  // Get branch details to get merchant ID
  const { data: branchDetails } = useBranchDetails(branchId || '', !!branchId)

  const { data: terminalsData, isLoading, error } = useQuery<TerminalsResponse>({
    queryKey: ['branch-terminals', branchId, page, search, sortBy, sortOrder],
    queryFn: async () => {
      const params = new URLSearchParams({
        page: page.toString(),
        page_size: '20',
        sort_by: sortBy,
        sort_order: sortOrder,
        search: search
      })
      
      const response = await fetch(`/api/branch-admins/${branchId}/terminals?${params}`)
      if (!response.ok) {
        throw new Error('Failed to fetch terminals')
      }
      return response.json()
    },
    enabled: !!branchId
  })

  const handleTerminalClick = (terminalId: string) => {
    // Navigate to terminal dashboard using the hierarchical route
    if (branchDetails?.merchant_id) {
      navigate({
        to: `/merchants/${branchDetails.merchant_id}/${branchId}/${terminalId}`
      })
    } else {
      console.warn('Merchant ID not available for navigation')
    }
  }

  if (isLoading) {
    return (
      <div className="container mx-auto p-6">
        <div className="space-y-6">
          <div className="flex items-center gap-4">
            <div className="h-8 w-8 bg-muted rounded animate-pulse" />
            <div className="h-8 w-48 bg-muted rounded animate-pulse" />
          </div>
          <div className="grid gap-4">
            {Array.from({ length: 10 }).map((_, i) => (
              <div key={i} className="h-20 bg-muted rounded animate-pulse" />
            ))}
          </div>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="container mx-auto p-6">
        <div className="text-center py-8">
          <p className="text-destructive">Error loading terminals</p>
        </div>
      </div>
    )
  }

  const terminals = terminalsData?.data || []
  const pagination = terminalsData?.pagination

  return (
    <div className="container mx-auto p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center gap-4">
        <Button
          variant="ghost"
          size="sm"
          onClick={() => navigate({ to: `/branch-admins/${branchId}` })}
        >
          <ArrowLeft className="h-4 w-4 mr-2" />
          Back to Branch
        </Button>
        <div>
          <h1 className="text-2xl font-bold">Terminals</h1>
          <p className="text-muted-foreground">
            {pagination?.total_items || 0} terminals found
          </p>
        </div>
      </div>

      {/* Filters */}
      <Card>
        <CardHeader>
          <CardTitle>Filters</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Search terminals..."
                  value={search}
                  onChange={(e) => setSearch(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            <Select value={sortBy} onValueChange={setSortBy}>
              <SelectTrigger className="w-48">
                <SelectValue placeholder="Sort by" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="total_amount">Total Amount</SelectItem>
                <SelectItem value="transaction_count">Transaction Count</SelectItem>
                <SelectItem value="terminal_id">Terminal ID</SelectItem>
              </SelectContent>
            </Select>
            <Select value={sortOrder} onValueChange={setSortOrder}>
              <SelectTrigger className="w-32">
                <SelectValue placeholder="Order" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="desc">Descending</SelectItem>
                <SelectItem value="asc">Ascending</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      {/* Terminals List */}
      <div className="space-y-4">
        {terminals.map((terminal) => {
          const initials = terminal.terminal_id.substring(0, 2).toUpperCase()
          
          return (
            <Card key={terminal.terminal_id} className="hover:shadow-md transition-shadow">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-4">
                    <Avatar className="h-12 w-12">
                      <AvatarFallback>
                        <Monitor className="h-6 w-6" />
                      </AvatarFallback>
                    </Avatar>
                    <div>
                      <h3 className="font-semibold">{terminal.terminal_id}</h3>
                      <p className="text-sm text-muted-foreground">
                        {terminal.transaction_count} transactions
                      </p>
                    </div>
                  </div>
                  <div className="flex items-center gap-4">
                    <div className="text-right">
                      <p className="font-semibold">₵{terminal.total_amount.toLocaleString()}</p>
                      <p className="text-sm text-muted-foreground">Total Volume</p>
                    </div>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleTerminalClick(terminal.terminal_id)}
                    >
                      <ExternalLink className="h-4 w-4 mr-2" />
                      View Details
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          )
        })}
      </div>

      {/* Pagination */}
      {pagination && pagination.total_pages > 1 && (
        <div className="flex items-center justify-between">
          <p className="text-sm text-muted-foreground">
            Showing {((pagination.page - 1) * pagination.page_size) + 1} to{' '}
            {Math.min(pagination.page * pagination.page_size, pagination.total_items)} of{' '}
            {pagination.total_items} terminals
          </p>
          <div className="flex gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => setPage(page - 1)}
              disabled={page <= 1}
            >
              Previous
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => setPage(page + 1)}
              disabled={page >= pagination.total_pages}
            >
              Next
            </Button>
          </div>
        </div>
      )}

      {terminals.length === 0 && (
        <Card>
          <CardContent className="text-center py-8">
            <Monitor className="h-12 w-12 mx-auto mb-4 text-muted-foreground" />
            <h3 className="text-lg font-semibold mb-2">No terminals found</h3>
            <p className="text-muted-foreground">
              {search ? 'Try adjusting your search criteria.' : 'This branch has no terminals.'}
            </p>
          </CardContent>
        </Card>
      )}
    </div>
  )
}

export const Route = createFileRoute('/_authenticated/branch-admins/$branchId/terminals')({
  component: TerminalsList,
})
