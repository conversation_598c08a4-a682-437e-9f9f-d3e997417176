import { createFileRoute } from '@tanstack/react-router'
import { useState } from 'react'
import { useQuery } from '@tanstack/react-query'
import { useBranchDetails } from '@/hooks/use-branches'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table'
import { ArrowLeft, Search, ChevronUp, ChevronDown, ChevronLeft, ChevronRight, ExternalLink } from 'lucide-react'
import { Header } from '@/components/layout/header'
import { Main } from '@/components/layout/main'
import { ProfileDropdown } from '@/components/profile-dropdown'
import { ThemeSwitch } from '@/components/theme-switch'
import { ErrorBoundary } from '@/components/error-boundary'
import { useNavigate } from '@tanstack/react-router'

interface PaginatedTerminalsResponse {
  data: Array<{
    terminal_id: string
    terminal_name?: string
    total_amount: number
    transaction_count: number
  }>
  pagination: {
    page: number
    page_size: number
    total_items: number
    total_pages: number
  }
  filters: any
  sort: {
    sort_by: string
    sort_order: string
  }
}

function BranchTerminals() {
  const { branchId } = Route.useParams()
  const navigate = useNavigate()
  const [page, setPage] = useState(1)
  const [pageSize] = useState(20)
  const [sortBy, setSortBy] = useState('total_amount')
  const [sortOrder, setSortOrder] = useState('desc')
  const [search, setSearch] = useState('')
  const [searchInput, setSearchInput] = useState('')

  // Get branch details to get merchant ID for navigation
  const { data: branchDetails } = useBranchDetails(branchId || '', !!branchId)

  const { data: terminalsData, isLoading, error } = useQuery<PaginatedTerminalsResponse>({
    queryKey: ['branch-terminals', branchId, page, pageSize, sortBy, sortOrder, search],
    queryFn: async () => {
      const params = new URLSearchParams({
        page: page.toString(),
        page_size: pageSize.toString(),
        sort_by: sortBy,
        sort_order: sortOrder,
        ...(search && { search })
      })

      const apiBaseUrl = import.meta.env.VITE_API_BASE_URL || 'http://localhost:8000'
      const response = await fetch(`${apiBaseUrl}/branch-admins/${branchId}/terminals?${params}`)
      if (!response.ok) throw new Error('Failed to fetch terminals')
      return response.json()
    },
    enabled: !!branchId,
  })

  const handleSearch = () => {
    setSearch(searchInput)
    setPage(1) // Reset to first page when searching
  }

  const handleSort = (column: string) => {
    if (sortBy === column) {
      setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc')
    } else {
      setSortBy(column)
      setSortOrder('desc')
    }
    setPage(1) // Reset to first page when sorting
  }

  const getSortIcon = (column: string) => {
    if (sortBy !== column) return null
    return sortOrder === 'asc' ? <ChevronUp className="h-4 w-4" /> : <ChevronDown className="h-4 w-4" />
  }

  const handleTerminalClick = (terminalId: string) => {
    // Navigate to terminal dashboard using the hierarchical route
    if (branchDetails?.merchant_id) {
      navigate({
        to: `/merchants/${branchDetails.merchant_id}/${branchId}/${terminalId}`
      })
    } else {
      console.warn('Merchant ID not available for navigation')
    }
  }

  return (
    <>
      {/* ===== Top Heading ===== */}
      <Header>
        <div className='ml-auto flex items-center space-x-4'>
          <ThemeSwitch />
          <ProfileDropdown />
        </div>
      </Header>

      {/* ===== Main ===== */}
      <Main>
        <div className="space-y-6">
          {/* Header */}
          <div className="space-y-4">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => navigate({ to: `/branch-admins/${branchId}` })}
              className="flex items-center gap-2"
            >
              <ArrowLeft className="h-4 w-4" />
              Back to Branch Dashboard
            </Button>
            <div>
              <h1 className="text-2xl font-bold">Branch Terminals</h1>
              <p className="text-muted-foreground">
                {terminalsData?.pagination?.total_items || 0} terminals found
              </p>
            </div>
          </div>

          {/* Search */}
          <div className="flex items-center gap-4">
            <div className="flex-1 max-w-sm">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Search terminals..."
                  value={searchInput}
                  onChange={(e) => setSearchInput(e.target.value)}
                  onKeyDown={(e) => e.key === 'Enter' && handleSearch()}
                  className="pl-10"
                />
              </div>
            </div>
            <Button onClick={handleSearch}>Search</Button>
            {search && (
              <Button variant="outline" onClick={() => {
                setSearch('')
                setSearchInput('')
                setPage(1)
              }}>
                Clear
              </Button>
            )}
          </div>

          {/* Results */}
          <Card>
            <CardHeader>
              <CardTitle>Terminals</CardTitle>
              <CardDescription>
                Showing {((page - 1) * pageSize) + 1} to {Math.min(page * pageSize, terminalsData?.pagination?.total_items || 0)} of {terminalsData?.pagination?.total_items || 0} terminals
              </CardDescription>
            </CardHeader>
            <CardContent>
              <ErrorBoundary>
                {isLoading ? (
                  <div className="flex justify-center py-8">
                    <div className="text-muted-foreground">Loading terminals...</div>
                  </div>
                ) : error ? (
                  <div className="flex justify-center py-8">
                    <div className="text-destructive">Error loading terminals</div>
                  </div>
                ) : (
                  <>
                    <Table>
                      <TableHeader>
                        <TableRow>
                          <TableHead
                            className="cursor-pointer hover:bg-muted/50"
                            onClick={() => handleSort('terminal_id')}
                          >
                            Terminal ID {getSortIcon('terminal_id')}
                          </TableHead>
                          <TableHead>
                            Name
                          </TableHead>
                          <TableHead
                            className="text-right cursor-pointer hover:bg-muted/50"
                            onClick={() => handleSort('total_amount')}
                          >
                            Total Amount {getSortIcon('total_amount')}
                          </TableHead>
                          <TableHead
                            className="text-right cursor-pointer hover:bg-muted/50"
                            onClick={() => handleSort('transaction_count')}
                          >
                            Transactions {getSortIcon('transaction_count')}
                          </TableHead>
                          <TableHead>Actions</TableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {terminalsData?.data?.map((terminal) => (
                          <TableRow key={terminal.terminal_id}>
                            <TableCell className="font-medium">
                              {terminal.terminal_id}
                            </TableCell>
                            <TableCell>
                              {terminal.terminal_name || '-'}
                            </TableCell>
                            <TableCell className="text-right">
                              ₵{terminal.total_amount.toLocaleString()}
                            </TableCell>
                            <TableCell className="text-right">
                              {terminal.transaction_count.toLocaleString()}
                            </TableCell>
                            <TableCell>
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={() => handleTerminalClick(terminal.terminal_id)}
                                className="flex items-center gap-2"
                              >
                                <ExternalLink className="h-4 w-4" />
                                View Dashboard
                              </Button>
                            </TableCell>
                          </TableRow>
                        ))}
                      </TableBody>
                    </Table>

                    {/* Pagination */}
                    <div className="flex items-center justify-between mt-4">
                      <div className="text-sm text-muted-foreground">
                        Page {page} of {terminalsData?.pagination?.total_pages || 1}
                      </div>
                      <div className="flex items-center gap-2">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => setPage(page - 1)}
                          disabled={page <= 1}
                        >
                          <ChevronLeft className="h-4 w-4" />
                          Previous
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => setPage(page + 1)}
                          disabled={page >= (terminalsData?.pagination?.total_pages || 1)}
                        >
                          Next
                          <ChevronRight className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                  </>
                )}
              </ErrorBoundary>
            </CardContent>
          </Card>
        </div>
      </Main>
    </>
  )
}

export const Route = createFileRoute('/_authenticated/branch-admins/$branchId/terminals')({
  component: BranchTerminals,
})
