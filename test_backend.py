#!/usr/bin/env python3
"""
Simple script to test the RPAY Analytics API endpoints
"""

import requests
import json
from typing import Dict, Any

BASE_URL = "http://localhost:8000"

def test_endpoint(endpoint: str, description: str) -> Dict[str, Any]:
    """Test a single API endpoint"""
    url = f"{BASE_URL}{endpoint}"
    print(f"\n🧪 Testing: {description}")
    print(f"📍 URL: {url}")
    
    try:
        response = requests.get(url, timeout=10)
        print(f"✅ Status: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"📊 Response preview: {json.dumps(data, indent=2)[:200]}...")
            return {"success": True, "data": data}
        else:
            print(f"❌ Error: {response.text}")
            return {"success": False, "error": response.text}
            
    except requests.exceptions.ConnectionError:
        print("❌ Connection Error: Is the backend server running?")
        return {"success": False, "error": "Connection failed"}
    except Exception as e:
        print(f"❌ Error: {str(e)}")
        return {"success": False, "error": str(e)}

def main():
    print("🚀 RPAY Analytics API Test Suite")
    print("=" * 50)
    
    # Test basic connectivity
    test_endpoint("/docs", "API Documentation")
    
    # Test agent endpoints
    test_endpoint("/agents/count", "Total Agent Count")
    agents_result = test_endpoint("/agents/list", "Agents List")

    # Get first agent ID from the list if available
    sample_agent_id = "AGT001"  # Default fallback
    if agents_result and agents_result.get("success") and agents_result.get("data"):
        agents_data = agents_result["data"]
        if len(agents_data) > 0:
            sample_agent_id = agents_data[0]["id"]
            print(f"📋 Using first agent from list: {sample_agent_id}")
        else:
            print("⚠️  No agents found in the system")
    
    test_endpoint(f"/agents/{sample_agent_id}/stats", f"Agent {sample_agent_id} Stats")
    test_endpoint(f"/agents/{sample_agent_id}/overview", f"Agent {sample_agent_id} Overview")
    test_endpoint(f"/agents/{sample_agent_id}/transaction-volume?granularity=monthly", 
                 f"Agent {sample_agent_id} Transaction Volume")
    test_endpoint(f"/agents/{sample_agent_id}/top-customers?mode=amount&limit=5", 
                 f"Agent {sample_agent_id} Top Customers")
    
    print("\n" + "=" * 50)
    print("🏁 Test completed!")
    print("\n💡 Tips:")
    print("- If you get connection errors, make sure the backend is running:")
    print("  cd rpay-analytics-api && source .venv/bin/activate && uvicorn app.main:app --reload")
    print("- If you get 404 errors for agent endpoints, check that you have transaction data loaded")
    print("- Replace 'AGT001' in this script with an actual agent ID from your data")

if __name__ == "__main__":
    main()
